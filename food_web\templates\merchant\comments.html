{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>用户评论管理 - 中华美食网</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #f0f0f0;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.3);
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 10px;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
        }
        .comments-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .comments-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .comments-card h5 .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
        .comment-item {
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        .comment-item:hover {
            border-color: #ff6b35;
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.1);
            background: white;
        }
        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .comment-user {
            display: flex;
            align-items: center;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 12px;
        }
        .user-info h6 {
            margin: 0;
            color: #8b4513;
            font-weight: 600;
        }
        .user-info small {
            color: #6c757d;
        }
        .comment-rating {
            display: flex;
            align-items: center;
        }
        .rating-stars {
            color: #ffc107;
            margin-right: 10px;
        }
        .rating-text {
            color: #ff6b35;
            font-weight: 600;
        }
        .comment-content {
            color: #495057;
            line-height: 1.6;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #ff6b35;
        }
        .comment-food {
            background: #fff8f0;
            padding: 10px 15px;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 10px;
        }
        .comment-food strong {
            color: #8b4513;
        }
        .comment-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .btn-reply {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        .btn-hide {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .filter-section h6 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state .icon {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>💬 用户评论管理</h2>
                    <p class="mb-0">倾听顾客声音，提升服务质量 - {{ user_name }}（商家）</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">🏠 返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 主要功能区 -->
                    <div class="sidebar-section">
                        <h5>🏪 核心功能</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/merchant/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">仪表板</span>
                            </a>
                            <a class="nav-link" href="/merchant/foods/">
                                <span class="nav-icon">🍽️</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link" href="/merchant/foods/add/">
                                <span class="nav-icon">➕</span>
                                <span class="nav-text">添加菜品</span>
                            </a>
                            <a class="nav-link active" href="/merchant/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">用户评论</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 数据分析区 -->
                    <div class="sidebar-section">
                        <h5>📊 数据分析</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/merchant/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分分析</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/sales/">
                                <span class="nav-icon">📈</span>
                                <span class="nav-text">销售统计</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/ingredients/">
                                <span class="nav-icon">🥘</span>
                                <span class="nav-text">配料分析</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/categories/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">类别统计</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 统计卡片 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">💬</div>
                            <div class="stats-number">{{ total_comments }}</div>
                            <p class="stats-label">总评论数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">⭐</div>
                            <div class="stats-number">{{ avg_comment_rating|floatformat:1 }}</div>
                            <p class="stats-label">平均评分</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">📅</div>
                            <div class="stats-number">{{ today_comments }}</div>
                            <p class="stats-label">今日新增</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">🔥</div>
                            <div class="stats-number">{{ high_rating_comments }}</div>
                            <p class="stats-label">好评数量</p>
                        </div>
                    </div>
                </div>

                <!-- 筛选区域 -->
                <div class="filter-section">
                    <h6>🔍 筛选评论</h6>
                    <form method="get" class="row align-items-center">
                        <div class="col-md-3">
                            <select name="rating" class="form-control">
                                <option value="">所有评分</option>
                                <option value="5" {% if request.GET.rating == '5' %}selected{% endif %}>5星好评</option>
                                <option value="4" {% if request.GET.rating == '4' %}selected{% endif %}>4星</option>
                                <option value="3" {% if request.GET.rating == '3' %}selected{% endif %}>3星</option>
                                <option value="2" {% if request.GET.rating == '2' %}selected{% endif %}>2星</option>
                                <option value="1" {% if request.GET.rating == '1' %}selected{% endif %}>1星差评</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" name="food_name" class="form-control" placeholder="搜索菜品名称" value="{{ request.GET.food_name }}">
                        </div>
                        <div class="col-md-3">
                            <input type="text" name="user_name" class="form-control" placeholder="搜索用户名" value="{{ request.GET.user_name }}">
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary mr-2" style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                                🔍 筛选
                            </button>
                            <a href="/merchant/comments/" class="btn btn-secondary">重置</a>
                        </div>
                    </form>
                </div>

                <!-- 评论列表 -->
                <div class="comments-card">
                    <h5>
                        <span class="icon">💬</span>
                        用户评论列表
                    </h5>
                    
                    {% if comments %}
                        {% for comment in comments %}
                        <div class="comment-item">
                            <div class="comment-header">
                                <div class="comment-user">
                                    <div class="user-avatar">
                                        {{ comment.user_name|first }}
                                    </div>
                                    <div class="user-info">
                                        <h6>{{ comment.user_name }}</h6>
                                        <small>{{ comment.created_at|date:"Y-m-d H:i" }}</small>
                                    </div>
                                </div>
                                <div class="comment-rating">
                                    <div class="rating-stars">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= comment.rating|floatformat:0 %}⭐{% else %}☆{% endif %}
                                        {% endfor %}
                                    </div>
                                    <span class="rating-text">{{ comment.rating }}分</span>
                                </div>
                            </div>
                            
                            <div class="comment-food">
                                <strong>菜品：</strong>{{ comment.food_name }}
                            </div>
                            
                            <div class="comment-content">
                                {{ comment.comment }}
                            </div>
                            
                            <div class="comment-actions">
                                <button class="btn btn-reply" onclick="replyComment({{ comment.id }})">
                                    💬 回复
                                </button>
                                {% if comment.is_approved %}
                                <button class="btn btn-hide" onclick="hideComment({{ comment.id }})">
                                    👁️ 隐藏
                                </button>
                                {% else %}
                                <button class="btn btn-reply" onclick="approveComment({{ comment.id }})">
                                    ✅ 显示
                                </button>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <div class="icon">💬</div>
                            <h5>暂无评论</h5>
                            <p class="text-muted">
                                {% if request.GET.rating or request.GET.food_name or request.GET.user_name %}
                                    没有找到符合条件的评论
                                {% else %}
                                    还没有用户发表评论
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script>
        function replyComment(commentId) {
            // 这里可以实现回复功能
            alert('回复功能开发中...');
        }

        function hideComment(commentId) {
            if (confirm('确定要隐藏这条评论吗？')) {
                // 发送AJAX请求隐藏评论
                $.post('/merchant/comments/hide/', {
                    'comment_id': commentId,
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('操作失败，请重试');
                    }
                });
            }
        }

        function approveComment(commentId) {
            if (confirm('确定要显示这条评论吗？')) {
                // 发送AJAX请求显示评论
                $.post('/merchant/comments/approve/', {
                    'comment_id': commentId,
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
                }, function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('操作失败，请重试');
                    }
                });
            }
        }
    </script>
</body>
</html>
