#!/usr/bin/env python
"""
美食推荐系统测试脚本
测试主要功能和界面优化效果
"""

import requests
import json
from urllib.parse import urljoin

class FoodSystemTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_homepage(self):
        """测试主页访问"""
        print("🏠 测试主页访问...")
        try:
            response = self.session.get(self.base_url)
            if response.status_code == 200:
                print("✅ 主页访问成功")
                return True
            else:
                print(f"❌ 主页访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 主页访问异常: {e}")
            return False
    
    def test_login_page(self):
        """测试登录页面"""
        print("🔐 测试登录页面...")
        try:
            response = self.session.get(urljoin(self.base_url, "/login/"))
            if response.status_code == 200:
                print("✅ 登录页面访问成功")
                return True
            else:
                print(f"❌ 登录页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登录页面访问异常: {e}")
            return False
    
    def test_food_categories(self):
        """测试食品分类页面"""
        print("🍽️ 测试食品分类页面...")
        categories = ["全部", "北京小吃", "天津小吃", "山西小吃", "蒙古小吃", 
                     "山东小吃", "新疆小吃", "重庆小吃", "河南小吃"]
        
        success_count = 0
        for category in categories:
            try:
                url = urljoin(self.base_url, f"/home/<USER>/")
                response = self.session.get(url)
                if response.status_code == 200:
                    print(f"✅ {category} 分类页面访问成功")
                    success_count += 1
                else:
                    print(f"❌ {category} 分类页面访问失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {category} 分类页面访问异常: {e}")
        
        print(f"📊 分类页面测试结果: {success_count}/{len(categories)} 成功")
        return success_count == len(categories)
    
    def test_chart_data_apis(self):
        """测试图表数据API"""
        print("📊 测试图表数据API...")
        apis = [
            "/home2_data/",
            "/home3_data/", 
            "/home4_data/",
            "/home5_data/"
        ]
        
        success_count = 0
        for api in apis:
            try:
                url = urljoin(self.base_url, api)
                response = self.session.get(url)
                if response.status_code == 200:
                    # 尝试解析JSON数据
                    data = response.json()
                    print(f"✅ {api} API访问成功，返回数据类型: {type(data)}")
                    success_count += 1
                else:
                    print(f"❌ {api} API访问失败，状态码: {response.status_code}")
            except json.JSONDecodeError:
                print(f"❌ {api} API返回数据不是有效JSON")
            except Exception as e:
                print(f"❌ {api} API访问异常: {e}")
        
        print(f"📊 图表API测试结果: {success_count}/{len(apis)} 成功")
        return success_count == len(apis)
    
    def test_static_files(self):
        """测试静态文件访问"""
        print("📁 测试静态文件访问...")
        static_files = [
            "/static/css/bootstrap.min.css",
            "/static/js/jquery.min.js",
            "/static/js/echarts.js"
        ]
        
        success_count = 0
        for file_path in static_files:
            try:
                url = urljoin(self.base_url, file_path)
                response = self.session.get(url)
                if response.status_code == 200:
                    print(f"✅ {file_path} 静态文件访问成功")
                    success_count += 1
                else:
                    print(f"❌ {file_path} 静态文件访问失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {file_path} 静态文件访问异常: {e}")
        
        print(f"📊 静态文件测试结果: {success_count}/{len(static_files)} 成功")
        return success_count == len(static_files)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始美食推荐系统测试...")
        print("=" * 50)
        
        tests = [
            ("主页访问", self.test_homepage),
            ("登录页面", self.test_login_page),
            ("食品分类", self.test_food_categories),
            ("图表数据API", self.test_chart_data_apis),
            ("静态文件", self.test_static_files)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 正在测试: {test_name}")
            print("-" * 30)
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        
        print("\n" + "=" * 50)
        print(f"🎯 测试总结: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常。")
        else:
            print("⚠️ 部分测试失败，请检查相关功能。")
        
        return passed == total

if __name__ == "__main__":
    tester = FoodSystemTester()
    tester.run_all_tests()
