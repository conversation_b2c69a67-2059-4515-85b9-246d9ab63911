{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>配料排行 - 商家管理中心</title>
    <style>
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
            margin-bottom: 20px;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>📊 配料排行</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #8b4513; margin-bottom: 20px;">📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #ff6b35;">
                        <h6 style="color: #8b4513; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📈 类别统计</a>
                        <a class="nav-link active" href="/merchant/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="chart-container">
                    <h4 style="color: #8b4513; margin-bottom: 25px; text-align: center;">📊 热门配料排行榜</h4>
                    <div id="ingredientsBarChart" style="width: 100%; height: 600px;"></div>
                </div>
                
                <div class="chart-container">
                    <h5 style="color: #8b4513; margin-bottom: 15px;">📊 数据说明</h5>
                    <p class="text-muted">
                        此横向柱状图展示了使用频率最高的前20种配料。通过这个排行榜，您可以清楚地看到
                        哪些配料最受欢迎，哪些配料使用较少。这有助于您优化采购计划，
                        确保热门配料的充足供应，同时考虑是否需要调整菜品搭配。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化ECharts图表
        var chart = echarts.init(document.getElementById('ingredientsBarChart'));
        
        // 获取数据并渲染图表
        $.get('/merchant/charts/ingredients-bar/data/', function(data) {
            var option = {
                title: {
                    text: '热门配料排行榜 (Top 20)',
                    left: 'center',
                    textStyle: {
                        color: '#8b4513',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: '{b}: 使用了 {c} 次'
                },
                grid: {
                    left: '15%',
                    right: '4%',
                    bottom: '3%',
                    top: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    name: '使用次数',
                    nameTextStyle: {
                        color: '#8b4513'
                    },
                    axisLabel: {
                        color: '#8b4513'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ff6b35'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#f0f0f0'
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    data: data.name,
                    axisLabel: {
                        color: '#8b4513',
                        fontSize: 12
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ff6b35'
                        }
                    }
                },
                series: [{
                    name: '使用次数',
                    type: 'bar',
                    data: data.data,
                    itemStyle: {
                        color: function(params) {
                            // 根据排名设置不同的颜色
                            var colors = [
                                '#ff6b35', '#f7931e', '#ffa726', '#ffb74d', '#ffcc02',
                                '#8bc34a', '#4caf50', '#26a69a', '#00acc1', '#0288d1',
                                '#3f51b5', '#673ab7', '#9c27b0', '#e91e63', '#f44336',
                                '#ff5722', '#795548', '#607d8b', '#9e9e9e', '#607d8b'
                            ];
                            return colors[params.dataIndex % colors.length];
                        },
                        borderRadius: [0, 4, 4, 0]
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        position: 'right',
                        color: '#8b4513',
                        fontWeight: 'bold',
                        formatter: '{c}'
                    }
                }]
            };
            
            chart.setOption(option);
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>
