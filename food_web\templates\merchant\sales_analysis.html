{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>销售统计 - 中华美食网</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
        }
        .sidebar h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 18px;
            border-radius: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(5px);
            text-decoration: none;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .sidebar hr {
            border-color: #ff6b35;
            margin: 20px 0;
        }
        .sidebar h6 {
            color: #8b4513;
            font-weight: 600;
            margin: 20px 0 15px 0;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 10px;
            line-height: 1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .chart-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .chart-card h5 .icon {
            margin-right: 10px;
            color: #ff6b35;
            font-size: 1.3rem;
        }
        .chart-container {
            height: 400px;
            width: 100%;
        }
        .mini-chart {
            height: 300px;
            width: 100%;
        }
        .popular-food-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        .popular-food-item:last-child {
            border-bottom: none;
        }
        .popular-food-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding-left: 10px;
            padding-right: 10px;
        }
        .food-rank {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .food-info {
            flex: 1;
        }
        .food-name {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .food-category {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .food-stats {
            text-align: right;
            color: #ff6b35;
            font-weight: 600;
        }
        .trend-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }
        .trend-up {
            background: #d4edda;
            color: #155724;
        }
        .trend-down {
            background: #f8d7da;
            color: #721c24;
        }
        .trend-stable {
            background: #fff3cd;
            color: #856404;
        }
        .section-title {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }
        .section-title .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>📈 销售统计</h2>
                    <p class="mb-0">数据驱动经营决策 - {{ user_name }}（商家）</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">🏠 返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <h5>📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr>
                        <h6>📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/ratings/">⭐ 评分分析</a>
                        <a class="nav-link active" href="/merchant/charts/sales/">📈 销售统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📊 类别统计</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 销售统计卡片 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">💰</div>
                            <div class="stats-number">{{ total_sales|default:"0" }}</div>
                            <p class="stats-label">总销售额</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">📦</div>
                            <div class="stats-number">{{ total_orders|default:"0" }}</div>
                            <p class="stats-label">总订单数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">👥</div>
                            <div class="stats-number">{{ total_customers|default:"0" }}</div>
                            <p class="stats-label">总顾客数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">📊</div>
                            <div class="stats-number">{{ avg_order_value|default:"0" }}</div>
                            <p class="stats-label">平均订单价值</p>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row">
                    <!-- 销售趋势图 -->
                    <div class="col-lg-8">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">📈</span>
                                销售趋势分析
                            </h5>
                            <div id="salesTrendChart" class="chart-container"></div>
                        </div>
                    </div>

                    <!-- 热门菜品排行 -->
                    <div class="col-lg-4">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">🔥</span>
                                热门菜品排行
                            </h5>
                            {% for food in popular_foods %}
                            <div class="popular-food-item">
                                <div class="food-rank">{{ forloop.counter }}</div>
                                <div class="food-info">
                                    <div class="food-name">{{ food.title }}</div>
                                    <div class="food-category">{{ food.caipu }}</div>
                                </div>
                                <div class="food-stats">
                                    {{ food.rate|floatformat:1 }}⭐
                                    <div style="font-size: 0.9rem; color: #6c757d;">
                                        评价 {{ food.rating_count|default:0 }}
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center text-muted py-4">
                                <p>暂无数据</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- 第二行图表 -->
                <div class="row">
                    <!-- 类别销售对比 -->
                    <div class="col-lg-6">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">🥘</span>
                                类别销售对比
                            </h5>
                            <div id="categorySalesChart" class="mini-chart"></div>
                        </div>
                    </div>

                    <!-- 月度销售统计 -->
                    <div class="col-lg-6">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">📅</span>
                                月度销售统计
                            </h5>
                            <div id="monthlySalesChart" class="mini-chart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 销售趋势图表
            var salesTrendChart = echarts.init(document.getElementById('salesTrendChart'));

            // 模拟销售趋势数据
            var salesTrendData = {
                dates: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                sales: [8500, 9200, 8800, 10500, 11200, 12800, 13500, 12900, 11800, 10200, 9800, 11500],
                orders: [85, 92, 88, 105, 112, 128, 135, 129, 118, 102, 98, 115]
            };

            var salesTrendOption = {
                title: {
                    text: '年度销售趋势',
                    left: 'center',
                    textStyle: {
                        color: '#8b4513',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['销售额', '订单数'],
                    bottom: '5%'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: salesTrendData.dates,
                    axisLabel: {
                        color: '#8b4513'
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '销售额(元)',
                        position: 'left',
                        axisLabel: {
                            color: '#8b4513',
                            formatter: '{value}'
                        }
                    },
                    {
                        type: 'value',
                        name: '订单数',
                        position: 'right',
                        axisLabel: {
                            color: '#8b4513',
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: '销售额',
                        type: 'line',
                        yAxisIndex: 0,
                        data: salesTrendData.sales,
                        smooth: true,
                        itemStyle: {
                            color: '#ff6b35'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: 'rgba(255, 107, 53, 0.3)'},
                                {offset: 1, color: 'rgba(255, 107, 53, 0.1)'}
                            ])
                        }
                    },
                    {
                        name: '订单数',
                        type: 'bar',
                        yAxisIndex: 1,
                        data: salesTrendData.orders,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#f7931e'},
                                {offset: 1, color: '#ffa726'}
                            ])
                        }
                    }
                ]
            };
            salesTrendChart.setOption(salesTrendOption);

            // 类别销售对比图表
            var categorySalesChart = echarts.init(document.getElementById('categorySalesChart'));

            var categorySalesData = [
                {name: '北京小吃', value: 2500},
                {name: '天津小吃', value: 1800},
                {name: '山西小吃', value: 2200},
                {name: '蒙古小吃', value: 1500},
                {name: '山东小吃', value: 2800},
                {name: '新疆小吃', value: 1600},
                {name: '重庆小吃', value: 3200},
                {name: '河南小吃', value: 1900}
            ];

            var categorySalesOption = {
                title: {
                    text: '各类别销售额',
                    left: 'center',
                    textStyle: {
                        color: '#8b4513',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: ¥{c} ({d}%)'
                },
                series: [{
                    type: 'pie',
                    radius: ['30%', '70%'],
                    center: ['50%', '60%'],
                    data: categorySalesData,
                    itemStyle: {
                        borderRadius: 5,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 12,
                            fontWeight: 'bold'
                        }
                    }
                }]
            };
            categorySalesChart.setOption(categorySalesOption);

            // 月度销售统计图表
            var monthlySalesChart = echarts.init(document.getElementById('monthlySalesChart'));

            var monthlySalesOption = {
                title: {
                    text: '近12个月销售额',
                    left: 'center',
                    textStyle: {
                        color: '#8b4513',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: '{b}: ¥{c}'
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: salesTrendData.dates,
                    axisLabel: {
                        fontSize: 10,
                        color: '#8b4513'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 10,
                        color: '#8b4513'
                    }
                },
                series: [{
                    data: salesTrendData.sales,
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#ff6b35'},
                            {offset: 1, color: '#f7931e'}
                        ]),
                        borderRadius: [4, 4, 0, 0]
                    }
                }]
            };
            monthlySalesChart.setOption(monthlySalesOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                salesTrendChart.resize();
                categorySalesChart.resize();
                monthlySalesChart.resize();
            });
        });
    </script>
</body>
</html>
