{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>销售统计 - 中华美食网</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #f0f0f0;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.3);
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 10px;
            line-height: 1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .chart-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .chart-card h5 .icon {
            margin-right: 10px;
            color: #ff6b35;
            font-size: 1.3rem;
        }
        .chart-container {
            height: 400px;
            width: 100%;
        }
        .mini-chart {
            height: 300px;
            width: 100%;
        }
        .popular-food-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        .popular-food-item:last-child {
            border-bottom: none;
        }
        .popular-food-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding-left: 10px;
            padding-right: 10px;
        }
        .food-rank {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .food-info {
            flex: 1;
        }
        .food-name {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .food-category {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .food-stats {
            text-align: right;
            color: #ff6b35;
            font-weight: 600;
        }
        .trend-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }
        .trend-up {
            background: #d4edda;
            color: #155724;
        }
        .trend-down {
            background: #f8d7da;
            color: #721c24;
        }
        .trend-stable {
            background: #fff3cd;
            color: #856404;
        }
        .section-title {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }
        .section-title .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>📊 数据统计分析</h2>
                    <p class="mb-0">基于真实数据的经营分析 - {{ user_name }}（商家）</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">🏠 返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 主要功能区 -->
                    <div class="sidebar-section">
                        <h5>🏪 核心功能</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/merchant/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">仪表板</span>
                            </a>
                            <a class="nav-link" href="/merchant/foods/">
                                <span class="nav-icon">🍽️</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link" href="/merchant/foods/add/">
                                <span class="nav-icon">➕</span>
                                <span class="nav-text">添加菜品</span>
                            </a>
                            <a class="nav-link" href="/merchant/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">用户评论</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 数据分析区 -->
                    <div class="sidebar-section">
                        <h5>📊 数据分析</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/merchant/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分分析</span>
                            </a>
                            <a class="nav-link active" href="/merchant/charts/sales/">
                                <span class="nav-icon">📈</span>
                                <span class="nav-text">数据统计</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/ingredients/">
                                <span class="nav-icon">🥘</span>
                                <span class="nav-text">配料分析</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/categories/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">类别统计</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 数据统计卡片 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">🍽️</div>
                            <div class="stats-number">{{ total_foods|default:"0" }}</div>
                            <p class="stats-label">菜品总数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">⭐</div>
                            <div class="stats-number">{{ total_ratings|default:"0" }}</div>
                            <p class="stats-label">评分总数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">💬</div>
                            <div class="stats-number">{{ total_comments|default:"0" }}</div>
                            <p class="stats-label">评论总数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">👥</div>
                            <div class="stats-number">{{ total_customers|default:"0" }}</div>
                            <p class="stats-label">用户总数</p>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row">
                    <!-- 菜品评分分布图 -->
                    <div class="col-lg-8">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">📊</span>
                                菜品评分分布分析
                            </h5>
                            <div id="ratingDistributionChart" class="chart-container"></div>
                        </div>
                    </div>

                    <!-- 热门菜品排行 -->
                    <div class="col-lg-4">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">🔥</span>
                                热门菜品排行
                            </h5>
                            {% for food in popular_foods %}
                            <div class="popular-food-item">
                                <div class="food-rank">{{ forloop.counter }}</div>
                                <div class="food-info">
                                    <div class="food-name">{{ food.title }}</div>
                                    <div class="food-category">{{ food.caipu }}</div>
                                </div>
                                <div class="food-stats">
                                    {{ food.rate|floatformat:1 }}⭐
                                    <div style="font-size: 0.9rem; color: #6c757d;">
                                        评分 {{ food.rating_count|default:0 }} | 评论 {{ food.comment_count|default:0 }}
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center text-muted py-4">
                                <p>暂无数据</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- 第二行图表 -->
                <div class="row">
                    <!-- 类别菜品数量对比 -->
                    <div class="col-lg-6">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">🥘</span>
                                各类别菜品数量
                            </h5>
                            <div id="categoryFoodsChart" class="mini-chart"></div>
                        </div>
                    </div>

                    <!-- 用户活跃度统计 -->
                    <div class="col-lg-6">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">👥</span>
                                用户活跃度统计
                            </h5>
                            <div id="userActivityChart" class="mini-chart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 菜品评分分布图表
            var ratingChart = echarts.init(document.getElementById('ratingDistributionChart'));

            // 获取真实的评分分布数据
            $.get('/merchant/charts/ratings/data/', function(data) {
                var ratingOption = {
                    title: {
                        text: '菜品评分分布',
                        left: 'center',
                        textStyle: {
                            color: '#8b4513',
                            fontSize: 16,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: {c}个菜品 ({d}%)'
                    },
                    legend: {
                        orient: 'horizontal',
                        bottom: '5%',
                        left: 'center'
                    },
                    series: [{
                        name: '评分分布',
                        type: 'pie',
                        radius: ['30%', '70%'],
                        center: ['50%', '45%'],
                        data: [
                            {name: '5.0分', value: data.excellent || 0, itemStyle: {color: '#ff6b35'}},
                            {name: '4.0-4.9分', value: data.good || 0, itemStyle: {color: '#f7931e'}},
                            {name: '3.0-3.9分', value: data.average || 0, itemStyle: {color: '#ffa726'}},
                            {name: '2.0-2.9分', value: data.poor || 0, itemStyle: {color: '#ffb74d'}},
                            {name: '1.0-1.9分', value: data.bad || 0, itemStyle: {color: '#ffcc02'}}
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            formatter: '{b}\n{d}%'
                        }
                    }]
                };
                ratingChart.setOption(ratingOption);
            }).fail(function() {
                // 如果获取数据失败，显示默认数据
                console.log('获取评分数据失败，使用默认数据');
            });

            // 类别菜品数量图表
            var categoryFoodsChart = echarts.init(document.getElementById('categoryFoodsChart'));

            $.get('/merchant/charts/categories/data/', function(data) {
                var categoryOption = {
                    title: {
                        text: '各类别菜品数量',
                        left: 'center',
                        textStyle: {
                            color: '#8b4513',
                            fontSize: 14
                        }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: {c}个菜品 ({d}%)'
                    },
                    series: [{
                        type: 'pie',
                        radius: ['30%', '70%'],
                        center: ['50%', '60%'],
                        data: data,
                        itemStyle: {
                            borderRadius: 5,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    }]
                };
                categoryFoodsChart.setOption(categoryOption);
            });

            // 用户活跃度统计图表
            var userActivityChart = echarts.init(document.getElementById('userActivityChart'));

            // 基于真实数据的用户活跃度统计
            var userActivityData = {
                categories: ['评分用户', '评论用户', '注册用户'],
                values: [{{ total_ratings }}, {{ total_comments }}, {{ total_customers }}]
            };

            var userActivityOption = {
                title: {
                    text: '用户活跃度',
                    left: 'center',
                    textStyle: {
                        color: '#8b4513',
                        fontSize: 14
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: '{b}: {c}人'
                },
                grid: {
                    left: '15%',
                    right: '10%',
                    bottom: '15%',
                    top: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: userActivityData.categories,
                    axisLabel: {
                        fontSize: 10,
                        color: '#8b4513'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 10,
                        color: '#8b4513'
                    }
                },
                series: [{
                    data: userActivityData.values,
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#ff6b35'},
                            {offset: 1, color: '#f7931e'}
                        ]),
                        borderRadius: [4, 4, 0, 0]
                    },
                    label: {
                        show: true,
                        position: 'top',
                        color: '#8b4513',
                        fontWeight: 'bold'
                    }
                }]
            };
            userActivityChart.setOption(userActivityOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                ratingChart.resize();
                categoryFoodsChart.resize();
                userActivityChart.resize();
            });
        });
    </script>
</body>
</html>
