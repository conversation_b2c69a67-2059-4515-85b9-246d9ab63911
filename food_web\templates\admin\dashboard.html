{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>系统管理控制台 - 中华美食网</title>
    <style>
        body {
            background-color: #f4f6f9;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        .admin-header .content {
            position: relative;
            z-index: 1;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #e8ecf4;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #e8ecf4;
        }
        .sidebar .nav-link {
            color: #667eea;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        .sidebar .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #764ba2;
            border-radius: 0 4px 4px 0;
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #e8ecf4;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
            line-height: 1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }
        .management-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        .management-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .management-card h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .management-card h5 .icon {
            margin-right: 10px;
            color: #764ba2;
            font-size: 1.3rem;
        }
        .quick-action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        .quick-action-card {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid #e8ecf4;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }
        .quick-action-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
            text-decoration: none;
            color: inherit;
        }
        .quick-action-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        .quick-action-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
        .quick-action-desc {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }
        .system-status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e8ecf4;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-label {
            color: #667eea;
            font-weight: 500;
        }
        .status-value {
            font-weight: 600;
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-danger { color: #dc3545; }
        .chart-container {
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2>👑 系统管理控制台</h2>
                        <p class="mb-0">全局系统管理与监控 - {{ user_name }}（超级管理员）</p>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group">
                            <button class="btn btn-outline-light" onclick="refreshSystemStatus()">
                                🔄 刷新状态
                            </button>
                            <a href="/logout/" class="btn btn-outline-light">
                                🚪 退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 系统管理区 -->
                    <div class="sidebar-section">
                        <h5>🎛️ 系统管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link active" href="/system-admin/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">控制台</span>
                            </a>
                            <a class="nav-link" href="/system-admin/users/">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">用户管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/model-weights/">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">模型权重</span>
                            </a>
                            <a class="nav-link" href="/system-admin/system-monitor/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">系统监控</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 内容管理区 -->
                    <div class="sidebar-section">
                        <h5>🍽️ 内容管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/foods/">
                                <span class="nav-icon">📋</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">评论管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分管理</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 数据分析区 -->
                    <div class="sidebar-section">
                        <h5>📈 数据分析</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/analytics/overview/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">数据概览</span>
                            </a>
                            <a class="nav-link" href="/system-admin/analytics/users/">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">用户分析</span>
                            </a>
                            <a class="nav-link" href="/system-admin/analytics/foods/">
                                <span class="nav-icon">🍽️</span>
                                <span class="nav-text">菜品分析</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/settings/">
                                <span class="nav-icon">🔧</span>
                                <span class="nav-text">系统配置</span>
                            </a>
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 系统核心统计 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">👥</div>
                            <div class="stats-number">{{ total_users|default:"0" }}</div>
                            <p class="stats-label">系统用户总数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">🍽️</div>
                            <div class="stats-number">{{ total_foods|default:"0" }}</div>
                            <p class="stats-label">菜品数据总量</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">⭐</div>
                            <div class="stats-number">{{ total_ratings|default:"0" }}</div>
                            <p class="stats-label">用户评价总数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">💬</div>
                            <div class="stats-number">{{ total_comments|default:"0" }}</div>
                            <p class="stats-label">用户评论总数</p>
                        </div>
                    </div>
                </div>

                <!-- 快速操作区域 -->
                <div class="management-card">
                    <h5>
                        <span class="icon">⚡</span>
                        快速操作
                    </h5>
                    <div class="quick-action-grid">
                        <a href="/system-admin/users/" class="quick-action-card">
                            <div class="quick-action-icon">👥</div>
                            <div class="quick-action-title">用户管理</div>
                            <p class="quick-action-desc">管理系统用户权限</p>
                        </a>
                        <a href="/system-admin/model-weights/" class="quick-action-card">
                            <div class="quick-action-icon">⚙️</div>
                            <div class="quick-action-title">模型权重</div>
                            <p class="quick-action-desc">调整推荐算法参数</p>
                        </a>
                        <a href="/system-admin/foods/" class="quick-action-card">
                            <div class="quick-action-icon">🍽️</div>
                            <div class="quick-action-title">菜品管理</div>
                            <p class="quick-action-desc">管理菜品数据库</p>
                        </a>
                        <a href="/system-admin/comments/" class="quick-action-card">
                            <div class="quick-action-icon">💬</div>
                            <div class="quick-action-title">评论管理</div>
                            <p class="quick-action-desc">审核用户评论</p>
                        </a>
                        <a href="/system-admin/system-monitor/" class="quick-action-card">
                            <div class="quick-action-icon">📊</div>
                            <div class="quick-action-title">系统监控</div>
                            <p class="quick-action-desc">监控系统运行状态</p>
                        </a>
                        <a href="/system-admin/analytics/overview/" class="quick-action-card">
                            <div class="quick-action-icon">📈</div>
                            <div class="quick-action-title">数据分析</div>
                            <p class="quick-action-desc">查看系统数据报表</p>
                        </a>
                    </div>
                </div>

                <!-- 系统状态监控 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="system-status-card">
                            <h5 style="color: #667eea; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 10px; color: #764ba2; font-size: 1.3rem;">🖥️</span>
                                系统运行状态
                            </h5>
                            <div class="status-item">
                                <span class="status-label">数据库连接</span>
                                <span class="status-value status-good">● 正常</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">推荐引擎</span>
                                <span class="status-value status-good">● 运行中</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">缓存系统</span>
                                <span class="status-value status-good">● 正常</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">API服务</span>
                                <span class="status-value status-good">● 正常</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">系统负载</span>
                                <span class="status-value status-good">● 低负载</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="management-card">
                            <h5>
                                <span class="icon">👥</span>
                                用户角色分布
                            </h5>
                            <div id="userRoleChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>

                <!-- 数据概览图表 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="management-card">
                            <h5>
                                <span class="icon">📊</span>
                                系统数据趋势
                            </h5>
                            <div id="systemTrendChart" class="chart-container"></div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="management-card">
                            <h5>
                                <span class="icon">🍽️</span>
                                菜品类别分布
                            </h5>
                            <div id="foodCategoryChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 用户角色分布图表
            var userRoleChart = echarts.init(document.getElementById('userRoleChart'));
            var userRoleOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}人 ({d}%)'
                },
                series: [{
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '50%'],
                    data: [
                        {name: '普通用户', value: {{ customer_count|default:0 }}, itemStyle: {color: '#28a745'}},
                        {name: '商家用户', value: {{ merchant_count|default:0 }}, itemStyle: {color: '#ff6b35'}},
                        {name: '管理员', value: {{ admin_count|default:0 }}, itemStyle: {color: '#667eea'}}
                    ],
                    label: {
                        show: true,
                        formatter: '{b}\n{d}%'
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            userRoleChart.setOption(userRoleOption);

            // 系统数据趋势图表
            var systemTrendChart = echarts.init(document.getElementById('systemTrendChart'));
            var systemTrendOption = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['用户数', '菜品数', '评价数'],
                    bottom: '5%'
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '用户数',
                        type: 'line',
                        data: [120, 132, 101, 134, 90, {{ total_users|default:0 }}],
                        smooth: true,
                        itemStyle: {color: '#667eea'}
                    },
                    {
                        name: '菜品数',
                        type: 'line',
                        data: [220, 182, 191, 234, 290, {{ total_foods|default:0 }}],
                        smooth: true,
                        itemStyle: {color: '#764ba2'}
                    },
                    {
                        name: '评价数',
                        type: 'line',
                        data: [150, 232, 201, 154, 190, {{ total_ratings|default:0 }}],
                        smooth: true,
                        itemStyle: {color: '#f093fb'}
                    }
                ]
            };
            systemTrendChart.setOption(systemTrendOption);

            // 菜品类别分布图表
            var foodCategoryChart = echarts.init(document.getElementById('foodCategoryChart'));

            // 获取菜品类别数据
            $.get('/system-admin/api/food-categories/', function(data) {
                var categoryOption = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: {c}个菜品'
                    },
                    series: [{
                        type: 'pie',
                        radius: '70%',
                        center: ['50%', '50%'],
                        data: data,
                        itemStyle: {
                            borderRadius: 5,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    }]
                };
                foodCategoryChart.setOption(categoryOption);
            }).fail(function() {
                // 如果获取数据失败，显示默认数据
                var defaultData = [
                    {name: '北京小吃', value: 25, itemStyle: {color: '#667eea'}},
                    {name: '天津小吃', value: 18, itemStyle: {color: '#764ba2'}},
                    {name: '山西小吃', value: 22, itemStyle: {color: '#f093fb'}},
                    {name: '其他', value: 35, itemStyle: {color: '#f8b500'}}
                ];

                var categoryOption = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: {c}个菜品'
                    },
                    series: [{
                        type: 'pie',
                        radius: '70%',
                        data: defaultData
                    }]
                };
                foodCategoryChart.setOption(categoryOption);
            });

            // 响应式调整
            window.addEventListener('resize', function() {
                userRoleChart.resize();
                systemTrendChart.resize();
                foodCategoryChart.resize();
            });
        });

        // 刷新系统状态
        function refreshSystemStatus() {
            // 这里可以添加AJAX请求来刷新系统状态
            alert('系统状态已刷新');
        }
    </script>
</body>
</html>
