{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>管理员控制台 - 美食推荐系统</title>
    <style>
        .admin-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.1);
            border-left: 5px solid #6c757d;
            transition: transform 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-number {
            font-size: 2.2rem;
            font-weight: bold;
            color: #6c757d;
        }
        .stats-label {
            color: #666;
            font-size: 1rem;
        }
        .admin-action-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #6f42c1;
            text-align: center;
            transition: all 0.3s;
        }
        .admin-action-card:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(111, 66, 193, 0.2);
        }
        .action-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .sidebar {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: #6f42c1;
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            text-decoration: none;
        }
        .user-stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.1);
        }
        .role-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .role-customer { background: #28a745; color: white; }
        .role-merchant { background: #ff6b35; color: white; }
        .role-admin { background: #6f42c1; color: white; }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>👑 管理员控制台</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（管理员）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #6f42c1; margin-bottom: 20px;">🎛️ 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="/system-admin/dashboard/">🏠 控制台</a>
                        <a class="nav-link" href="/system-admin/users/">👥 用户管理</a>
                        <a class="nav-link" href="/system-admin/model-weights/">⚙️ 模型权重</a>
                        <hr style="border-color: #6c757d;">
                        <h6 style="color: #6c757d; margin: 15px 0 10px 0;">🍽️ 菜品管理</h6>
                        <a class="nav-link" href="/system-admin/foods/">📋 菜品列表</a>
                        <a class="nav-link" href="/system-admin/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #6c757d;">
                        <h6 style="color: #6c757d; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/system-admin/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/system-admin/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/system-admin/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/system-admin/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 系统统计 -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">{{ total_users }}</div>
                            <div class="stats-label">👥 总用户数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">{{ total_foods }}</div>
                            <div class="stats-label">🍽️ 总菜品数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">{{ total_ratings }}</div>
                            <div class="stats-label">⭐ 总评价数</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-number">{{ admin_count }}</div>
                            <div class="stats-label">👑 管理员数</div>
                        </div>
                    </div>
                </div>

                <!-- 用户角色统计 -->
                <div class="user-stats-card mb-4">
                    <h4 style="color: #6f42c1; margin-bottom: 20px;">👥 用户角色分布</h4>
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="stats-number" style="color: #28a745;">{{ customer_count }}</div>
                            <span class="role-badge role-customer">订单人</span>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="stats-number" style="color: #ff6b35;">{{ merchant_count }}</div>
                            <span class="role-badge role-merchant">商家</span>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="stats-number" style="color: #6f42c1;">{{ admin_count }}</div>
                            <span class="role-badge role-admin">管理员</span>
                        </div>
                    </div>
                </div>

                <!-- 管理员快捷操作 -->
                <div class="row">
                    <div class="col-md-4">
                        <a href="/admin/users/" class="text-decoration-none">
                            <div class="admin-action-card">
                                <div class="action-icon">👥</div>
                                <h5 style="color: #6f42c1;">用户管理</h5>
                                <p class="text-muted">管理系统用户和权限</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/admin/model-weights/" class="text-decoration-none">
                            <div class="admin-action-card">
                                <div class="action-icon">⚙️</div>
                                <h5 style="color: #6f42c1;">模型权重</h5>
                                <p class="text-muted">调整推荐算法权重</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="/admin/charts/ingredients/" class="text-decoration-none">
                            <div class="admin-action-card">
                                <div class="action-icon">📊</div>
                                <h5 style="color: #6f42c1;">数据分析</h5>
                                <p class="text-muted">查看系统数据报表</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 最近菜品 -->
                <div class="user-stats-card">
                    <h4 style="color: #6f42c1; margin-bottom: 20px;">🆕 最近添加的菜品</h4>
                    {% for food in recent_foods %}
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div>
                            <h6 style="color: #6f42c1; margin-bottom: 5px;">{{ food.title }}</h6>
                            <small class="text-muted">{{ food.caipu }} | 评分: {{ food.rate }}</small>
                        </div>
                        <div>
                            <a href="/admin/foods/edit/{{ food.id }}/" class="btn btn-sm btn-outline-primary">编辑</a>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted text-center">暂无菜品数据</p>
                    {% endfor %}
                    
                    {% if recent_foods %}
                    <div class="text-center mt-3">
                        <a href="/admin/foods/" class="btn btn-primary" 
                           style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); border: none;">
                            查看所有菜品
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
