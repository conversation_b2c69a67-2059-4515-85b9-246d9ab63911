from django.db import models

# Create your models here.

# Create your models here.
class User(models.Model):
    ROLE_CHOICES = [
        ('customer', '订单人'),
        ('merchant', '商家'),
        ('admin', '管理员'),
    ]

    name = models.CharField(max_length=16,unique=True)
    password = models.CharField(max_length=16)
    age = models.CharField(max_length=16)
    types_interest = models.CharField(max_length=16)
    role = models.Char<PERSON>ield(max_length=10, choices=ROLE_CHOICES, default='customer')

    @classmethod
    def create(cls,name,password,age,types_interest,role='customer'):
        return cls(name=name,password=password,age=age,types_interest=types_interest,role=role)

    def __str__(self):
        return f"{self.name} ({self.get_role_display()})"


class Food(models.Model):
    title = models.Char<PERSON>ield(max_length=1024)
    href = models.Cha<PERSON><PERSON><PERSON>(max_length=1024)
    peiliao = models.Char<PERSON><PERSON>(max_length=1024)
    rate = models.Char<PERSON>ield(max_length=1024)
    food_id = models.CharField(max_length=1024)
    img = models.Char<PERSON>ield(max_length=1024)
    caipu = models.CharField(max_length=1024)


    @classmethod
    def create(cls,title,href,peiliao,rate,food_id,img,caipu):
        return cls(title=title,href=href,peiliao=peiliao,rate=rate,food_id=food_id,
                   img=img,caipu=caipu)

    def __str__(self):
        return self.title


#美食评分
class FoodRating(models.Model):
    food_id = models.CharField(max_length=1024)  #美食id
    name = models.CharField(max_length=1024)  #
    user_id = models.CharField(max_length=16)  # 用户id
    rating = models.CharField(max_length=16)  # 评分

    @classmethod
    def create(cls,food_id,name,user_id,rating):
        return cls(food_id=food_id,name=name,user_id=user_id,rating=rating)

    def __str__(self):
        return self.food_id


#美食评论
class FoodComment(models.Model):
    food_id = models.CharField(max_length=1024)  # 美食id
    food_name = models.CharField(max_length=1024)  # 美食名称
    user_id = models.CharField(max_length=16)  # 用户id
    user_name = models.CharField(max_length=16)  # 用户名
    comment = models.TextField()  # 评论内容
    rating = models.CharField(max_length=16, default='5')  # 评分
    created_at = models.DateTimeField(auto_now_add=True)  # 创建时间
    is_approved = models.BooleanField(default=True)  # 是否审核通过

    @classmethod
    def create(cls, food_id, food_name, user_id, user_name, comment, rating='5'):
        return cls(food_id=food_id, food_name=food_name, user_id=user_id,
                  user_name=user_name, comment=comment, rating=rating)

    def __str__(self):
        return f"{self.user_name} 对 {self.food_name} 的评论"

    class Meta:
        ordering = ['-created_at']  # 按创建时间倒序排列