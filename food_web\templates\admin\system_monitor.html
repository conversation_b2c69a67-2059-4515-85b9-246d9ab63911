{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>系统监控 - 中华美食网</title>
    <style>
        body {
            background-color: #f4f6f9;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        .admin-header .content {
            position: relative;
            z-index: 1;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #e8ecf4;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #e8ecf4;
        }
        .sidebar .nav-link {
            color: #667eea;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .monitor-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        .monitor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .monitor-card h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .monitor-card h5 .icon {
            margin-right: 10px;
            color: #764ba2;
            font-size: 1.3rem;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        .status-item {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            border-top: 4px solid #667eea;
        }
        .status-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .status-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        .status-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1;
        }
        .status-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-danger { color: #dc3545; }
        .status-info { color: #17a2b8; }
        .chart-container {
            height: 300px;
            width: 100%;
        }
        .log-item {
            background: #f8f9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .log-item:hover {
            background: #e8ecf4;
        }
        .log-time {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        .log-message {
            color: #667eea;
            font-weight: 500;
            margin: 5px 0;
        }
        .log-details {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .refresh-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .alert-item {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #ffc107;
        }
        .alert-item.danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
        }
        .alert-item.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2>📊 系统监控</h2>
                        <p class="mb-0">实时系统状态监控与性能分析 - {{ user_name }}（超级管理员）</p>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group">
                            <button class="btn btn-outline-light refresh-btn" onclick="refreshSystemData()">
                                🔄 刷新数据
                            </button>
                            <a href="/system-admin/dashboard/" class="btn btn-outline-light">
                                🏠 返回控制台
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 系统管理区 -->
                    <div class="sidebar-section">
                        <h5>🎛️ 系统管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">控制台</span>
                            </a>
                            <a class="nav-link" href="/system-admin/users/">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">用户管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/model-weights/">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">模型权重</span>
                            </a>
                            <a class="nav-link active" href="/system-admin/system-monitor/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">系统监控</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 内容管理区 -->
                    <div class="sidebar-section">
                        <h5>🍽️ 内容管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/foods/">
                                <span class="nav-icon">📋</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">评论管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分管理</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 系统状态概览 -->
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-icon status-good">🖥️</div>
                        <div class="status-value status-good">正常</div>
                        <p class="status-label">服务器状态</p>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-good">💾</div>
                        <div class="status-value status-good">连接</div>
                        <p class="status-label">数据库状态</p>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-warning">⚡</div>
                        <div class="status-value status-warning">75%</div>
                        <p class="status-label">CPU使用率</p>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-info">💿</div>
                        <div class="status-value status-info">60%</div>
                        <p class="status-label">内存使用率</p>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-good">🌐</div>
                        <div class="status-value status-good">{{ active_users|default:"0" }}</div>
                        <p class="status-label">在线用户</p>
                    </div>
                    <div class="status-item">
                        <div class="status-icon status-info">📊</div>
                        <div class="status-value status-info">{{ daily_requests|default:"0" }}</div>
                        <p class="status-label">今日请求</p>
                    </div>
                </div>

                <!-- 性能监控图表 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="monitor-card">
                            <h5>
                                <span class="icon">📈</span>
                                系统性能趋势
                            </h5>
                            <div id="performanceChart" class="chart-container"></div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="monitor-card">
                            <h5>
                                <span class="icon">👥</span>
                                用户活跃度
                            </h5>
                            <div id="userActivityChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>

                <!-- 系统日志和警报 -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="monitor-card">
                            <h5>
                                <span class="icon">📋</span>
                                系统日志
                            </h5>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <div class="log-item">
                                    <div class="log-time">2024-01-15 14:30:25</div>
                                    <div class="log-message">用户登录成功</div>
                                    <div class="log-details">用户ID: 1001, IP: *************</div>
                                </div>
                                <div class="log-item">
                                    <div class="log-time">2024-01-15 14:28:15</div>
                                    <div class="log-message">数据库连接重置</div>
                                    <div class="log-details">连接池: main_pool, 耗时: 120ms</div>
                                </div>
                                <div class="log-item">
                                    <div class="log-time">2024-01-15 14:25:10</div>
                                    <div class="log-message">推荐算法执行完成</div>
                                    <div class="log-details">处理用户: 50, 耗时: 2.3s</div>
                                </div>
                                <div class="log-item">
                                    <div class="log-time">2024-01-15 14:20:05</div>
                                    <div class="log-message">缓存清理完成</div>
                                    <div class="log-details">清理项目: 1250, 释放内存: 45MB</div>
                                </div>
                                <div class="log-item">
                                    <div class="log-time">2024-01-15 14:15:30</div>
                                    <div class="log-message">系统备份完成</div>
                                    <div class="log-details">备份大小: 2.1GB, 耗时: 15分钟</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="monitor-card">
                            <h5>
                                <span class="icon">⚠️</span>
                                系统警报
                            </h5>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <div class="alert-item success">
                                    <div style="font-weight: 600; color: #155724; margin-bottom: 5px;">
                                        ✅ 系统运行正常
                                    </div>
                                    <small style="color: #155724;">
                                        所有核心服务运行稳定
                                    </small>
                                </div>
                                <div class="alert-item">
                                    <div style="font-weight: 600; color: #856404; margin-bottom: 5px;">
                                        ⚠️ CPU使用率偏高
                                    </div>
                                    <small style="color: #856404;">
                                        当前CPU使用率75%，建议关注
                                    </small>
                                </div>
                                <div class="alert-item success">
                                    <div style="font-weight: 600; color: #155724; margin-bottom: 5px;">
                                        ✅ 数据库性能良好
                                    </div>
                                    <small style="color: #155724;">
                                        查询响应时间平均50ms
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 初始化性能趋势图表
            var performanceChart = echarts.init(document.getElementById('performanceChart'));
            var performanceOption = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['CPU使用率', '内存使用率', '网络流量'],
                    bottom: '5%'
                },
                xAxis: {
                    type: 'category',
                    data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
                },
                yAxis: {
                    type: 'value',
                    name: '使用率(%)'
                },
                series: [
                    {
                        name: 'CPU使用率',
                        type: 'line',
                        data: [45, 52, 68, 75, 82, 75],
                        smooth: true,
                        itemStyle: {color: '#667eea'}
                    },
                    {
                        name: '内存使用率',
                        type: 'line',
                        data: [35, 42, 55, 60, 65, 60],
                        smooth: true,
                        itemStyle: {color: '#764ba2'}
                    },
                    {
                        name: '网络流量',
                        type: 'line',
                        data: [20, 25, 35, 45, 40, 35],
                        smooth: true,
                        itemStyle: {color: '#f093fb'}
                    }
                ]
            };
            performanceChart.setOption(performanceOption);

            // 初始化用户活跃度图表
            var userActivityChart = echarts.init(document.getElementById('userActivityChart'));
            var userActivityOption = {
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                },
                yAxis: {
                    type: 'value',
                    name: '活跃用户数'
                },
                series: [{
                    name: '活跃用户',
                    type: 'bar',
                    data: [120, 200, 150, 180, 220, 300, 250],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#667eea'},
                            {offset: 1, color: '#764ba2'}
                        ])
                    }
                }]
            };
            userActivityChart.setOption(userActivityOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                performanceChart.resize();
                userActivityChart.resize();
            });
        });

        // 刷新系统数据
        function refreshSystemData() {
            // 显示加载状态
            $('.status-value').html('<div class="spinner-border spinner-border-sm" role="status"></div>');
            
            // 模拟数据刷新
            setTimeout(function() {
                $('.status-value').each(function(index) {
                    var values = ['正常', '连接', '78%', '62%', Math.floor(Math.random() * 100), Math.floor(Math.random() * 1000)];
                    $(this).text(values[index]);
                });
                
                // 显示成功消息
                alert('系统数据已刷新！');
            }, 2000);
        }
    </script>
</body>
</html>
