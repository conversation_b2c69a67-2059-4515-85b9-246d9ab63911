from django.shortcuts import render

# Create your views here.
from django.shortcuts import render, HttpResponse, redirect
from app01.models import User, Food,FoodRating
import time,json
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage, InvalidPage
from app01.decorators import login_required, customer_required, merchant_required, admin_required, merchant_or_admin_required


# 用户登录
def login(request):
    if request.method == "POST":
        name = request.POST.get("name")
        password = request.POST.get("password")
        user_objs = User.objects.filter(name=name).filter(password=password)
        if user_objs.exists():
            user_obj = user_objs.first()
            response = None

            # 根据用户角色跳转到不同界面
            if user_obj.role == 'customer':
                response = redirect("/customer/home/")
            elif user_obj.role == 'merchant':
                response = redirect("/merchant/dashboard/")
            elif user_obj.role == 'admin':
                response = redirect("/system-admin/dashboard/")
            else:
                response = redirect("/customer/home/")  # 默认跳转

            response.set_signed_cookie("name", name)
            response.set_signed_cookie("role", user_obj.role)
            request.session['islogin'] = True
            request.session['user_role'] = user_obj.role
            return response
    return render(request, "login.html")


# 用户注册
def reg(request):
    if request.method == "POST":
        name = request.POST.get("name")
        password = request.POST.get("password")
        age = request.POST.get("age")
        types_interest = request.POST.get("types_interest")
        role = request.POST.get("role", "customer")  # 默认为订单人

        obj = User.create(name, password, age, types_interest, role)
        obj.save()
        time.sleep(0.5)
        return redirect("/login/")
    return render(request, "reg.html")


# 大厅
def home(request,food_type):
    user_name = request.get_signed_cookie("name")

    if food_type == "全部":  # 全部
        result = Food.objects.all()
        print(len(result))
    else:
        result = Food.objects.filter(caipu__icontains=food_type)

    word = request.GET.get("word")
    if word:
        if food_type == "全部":  #全部
            result = Food.objects.filter(title__icontains=word)
            print(len(result))
        else:
            result = Food.objects.filter(caipu__icontains=food_type).filter(title__icontains=word)

    paginator = Paginator(result, 12)

    if request.method == "GET":
        page = request.GET.get('page')
        try:
            food_li = paginator.page(page)
        except PageNotAnInteger:
            food_li = paginator.page(1)
        except InvalidPage:
            return HttpResponse('找不到页面的内容')
        except EmptyPage:
            food_li = paginator.page(paginator.num_pages)
    return render(request, "home.html", {"food_li": food_li, "user_name": user_name,"food_type":food_type,"word":word})




#详情页
def home_food_detail(request,id):
    obj = Food.objects.filter(id=id).first()
    user_name = request.get_signed_cookie("name")

    user_id = User.objects.filter(name=user_name).first().id  # 用户id
    rating_obj = FoodRating.objects.filter(food_id=id).filter(user_id=user_id).first()
    if rating_obj == None:
        rating_obj = FoodRating.create(id, user_name, user_id, "0")
        rating_obj.save()
    print(rating_obj)
    if request.method == "POST":
        print(1)
        rating = request.POST.get("rating")
        print("rating",rating)
        rating_obj.rating = rating
        rating_obj.save()
        print(111)
        return redirect(f"/home_food_detail/{id}/")

    return render(request, "home_food_detail.html", {"user_name": user_name, "obj": obj, "rating_obj":rating_obj})



# 大厅
def home_reco(request):
    user_name = request.get_signed_cookie("name")
    types_interest = User.objects.filter(name=user_name).first().types_interest
    result = Food.objects.filter(caipu__icontains=types_interest)

    paginator = Paginator(result, 12)

    if request.method == "GET":
        page = request.GET.get('page')
        try:
            food_li = paginator.page(page)
        except PageNotAnInteger:
            food_li = paginator.page(1)
        except InvalidPage:
            return HttpResponse('找不到页面的内容')
        except EmptyPage:
            food_li = paginator.page(paginator.num_pages)
    return render(request, "home_reco.html", {"food_li": food_li, "user_name": user_name})




from numpy import *
import time
from texttable import Texttable


class CF:

    def __init__(self, movies, ratings, k=5, n=10):
        self.movies = movies
        self.ratings = ratings
        self.k = k
        self.n = n
        self.userDict = {}

        self.ItemUser = {}
        self.neighbors = []
        self.recommandList = []
        self.cost = 0.0

    def recommendByUser(self, userId):
        self.formatRate()
        self.n = len(self.userDict[userId])
        self.getNearestNeighbor(userId)
        self.getrecommandList(userId)
        self.getPrecision(userId)

    def getrecommandList(self, userId):
        self.recommandList = []
        recommandDict = {}
        for neighbor in self.neighbors:
            movies = self.userDict[neighbor[1]]
            for movie in movies:
                if (movie[0] in recommandDict):
                    recommandDict[movie[0]] += neighbor[0]
                else:
                    recommandDict[movie[0]] = neighbor[0]

        # 建立推荐列表
        for key in recommandDict:
            self.recommandList.append([recommandDict[key], key])
        self.recommandList.sort(reverse=True)
        self.recommandList = self.recommandList[:self.n]

    # 将ratings转换为userDict和ItemUser
    def formatRate(self):
        self.userDict = {}
        self.ItemUser = {}
        for i in self.ratings:
            # 评分最高为5 除以5 进行数据归一化
            temp = (i[1], float(i[2]) / 5)
            # 计算userDict {'1':[(1,5),(2,5)...],'2':[...]...}
            if (i[0] in self.userDict):
                self.userDict[i[0]].append(temp)
            else:
                self.userDict[i[0]] = [temp]
            # 计算ItemUser {'1',[1,2,3..],...}
            if (i[1] in self.ItemUser):
                self.ItemUser[i[1]].append(i[0])
            else:
                self.ItemUser[i[1]] = [i[0]]

    # 找到某用户的相邻用户
    def getNearestNeighbor(self, userId):
        neighbors = []
        self.neighbors = []
        # 获取userId评分的图书都有那些用户也评过分
        for i in self.userDict[userId]:
            for j in self.ItemUser[i[0]]:
                if (j != userId and j not in neighbors):
                    neighbors.append(j)
        # 计算这些用户与userId的相似度并排序
        for i in neighbors:
            dist = self.getCost(userId, i)
            self.neighbors.append([dist, i])
        # 排序默认是升序，reverse=True表示降序
        self.neighbors.sort(reverse=True)
        self.neighbors = self.neighbors[:self.k]

    # 格式化userDict数据
    def formatuserDict(self, userId, l):
        user = {}
        for i in self.userDict[userId]:
            user[i[0]] = [i[1], 0]
        for j in self.userDict[l]:
            if (j[0] not in user):
                user[j[0]] = [0, j[1]]
            else:
                user[j[0]][1] = j[1]
        return user

    # 计算余弦距离
    def getCost(self, userId, l):
        user = self.formatuserDict(userId, l)
        x = 0.0
        y = 0.0
        z = 0.0
        for k, v in user.items():
            x += float(v[0]) * float(v[0])
            y += float(v[1]) * float(v[1])
            z += float(v[0]) * float(v[1])
        if (z == 0.0):
            return 0
        return z / sqrt(x * y)

    # 推荐的准确率
    def getPrecision(self, userId):
        user = [i[0] for i in self.userDict[userId]]
        print("self.recommandList",self.recommandList)
        recommand = [i[1] for i in self.recommandList]
        print(recommand)
        count = 0.0
        if (len(user) >= len(recommand)):
            for i in recommand:
                if (i in user):
                    count += 1.0
            self.cost = count / len(recommand)
        else:
            for i in user:
                if (i in recommand):
                    count += 1.0
            self.cost = count / len(user)

    # 显示推荐列表
    def showTable(self):
        neighbors_id = [i[1] for i in self.neighbors]
        table = Texttable()
        table.set_deco(Texttable.HEADER)
        table.set_cols_dtype(["t", "t", "t", "t"])
        table.set_cols_align(["l", "l", "l", "l"])
        rows = []
        # rows.append([u"movie ID", u"Name", u"release", u"from userID"])
        for item in self.recommandList:
            fromID = []
            for i in self.movies:
                if i[0] == item[1]:
                    movie = i
                    break
            for i in self.ItemUser[item[1]]:
                if i in neighbors_id:
                    fromID.append(i)
            movie.append(fromID)
            rows.append(movie)
        print(rows)
        return rows


# 算法推荐
def home_algorithm_reco(request):
    user_name = request.get_signed_cookie("name")
    print("user_name",user_name)
    user_id = User.objects.filter(name=user_name).first().id
    print("user_id", user_id)
    foods = []
    food_li = Food.objects.all()
    for food in food_li:
        row = []
        row.append(str(food.id))
        row.append(food.title)
        row.append(food.caipu)
        foods.append(row)
    print("foods", foods)
    ratings = []  # 用户id   游戏id  评分
    rating_li = FoodRating.objects.all()
    for rating in rating_li:
        row2 = []
        row2.append(rating.user_id)
        row2.append(rating.food_id)
        row2.append(rating.rating)
        ratings.append(row2)
    print("ratings",ratings)

    demo = CF(foods, ratings, k=20)
    print("demo----",demo)
    demo.recommendByUser(str(user_id))  # 获取当前用户推荐的
    li = demo.showTable()
    print(li)
    result = []
    for i in li:
        obj = Food.objects.filter(id=i[0]).first()
        result.append(obj)
    print(result)
    paginator = Paginator(result[:20], 12)

    if request.method == "GET":
        # 获取 url 后面的 page 参数的值, 首页不显示 page 参数, 默认值是 1
        page = request.GET.get('page')
        try:
            food_li = paginator.page(page)
        # todo: 注意捕获异常
        except PageNotAnInteger:
            # 如果请求的页数不是整数, 返回第一页。
            food_li = paginator.page(1)
        except InvalidPage:
            # 如果请求的页数不存在, 重定向页面
            return HttpResponse('找不到页面的内容')
        except EmptyPage:
            # 如果请求的页数不在合法的页数范围内，返回结果的最后一页。
            food_li = paginator.page(paginator.num_pages)
    return render(request, "home_algorithm_reco.html", {"food_li": food_li, "user_name": user_name})






def home2(request):
    user_name = request.get_signed_cookie("name")
    return render(request,"home2.html",{"user_name": user_name})

import copy
def home2_data(request):
    li = Food.objects.all()
    rows = []
    for i in li:
        rows.extend(i.peiliao.split(","))
    rows2 = copy.deepcopy(rows)
    rows2 = list(set(rows2))
    dic = {}
    for row2 in rows2:
        dic[row2] = 0
    for row in rows:

        try:
            dic[row] += 1
        except:
            pass
    result = []
    for k, v in dic.items():
        dic3 = {}
        dic3["name"] = f"{k}:{v}"
        dic3["value"] = v
        result.append(dic3)
    print(result)
    return HttpResponse(json.dumps(result))




# 评分占比
def home3(request):
    user_name = request.get_signed_cookie("name")
    return render(request, "home3.html",{"user_name":user_name})


def home3_data(request):
    li = Food.objects.all()
    dic = {}
    li2 = []
    for i in li:
        li2.append(i.caipu.strip("小吃"))
    li3 = list(set(li2))
    result = []
    for i3 in li3:
        dic3 = {}
        dic3["name"] = i3
        dic3["value"] = li2.count(i3)
        result.append(dic3)
    print(result)
    return HttpResponse(json.dumps(result))




# 柱状图
def home4(request):
    user_name = request.get_signed_cookie("name")
    return render(request, "home4.html",{"user_name":user_name})


def home4_data(request):
    dic4 = {
        "categories": [],
        "data": [],
    }
    li = Food.objects.all()
    rows = list(set([i.caipu for i in li]))
    dic = {}
    for row in rows:
        dic[row] = 0
    for i in li:
        dic[i.caipu]+=1
    print(dic)
    for k, v in dic.items():
        dic4['categories'].append(k)
        dic4['data'].append(v)
    return HttpResponse(json.dumps(dic4))


# 配料柱状图
def home5(request):
    user_name = request.get_signed_cookie("name")
    return render(request, "home5.html",{"user_name":user_name})


def home5_data(request):
    dic4 = {
        "categories": [],
        "data": [],
    }
    # li = Food.objects.all()
    #
    # rows = list(set([i.peiliao.split(',') for i in li]))
    # dic = {}
    # for row in rows:
    #     dic[row] = 0
    # for i in li:
    #     dic[i.peiliao.split(',')]+=1
    # print(dic)

    li = Food.objects.all()
    rows = []
    for i in li:
        rows.extend(i.peiliao.split(','))
    rows2 = copy.deepcopy(rows)
    rows2 = list(set(rows2))
    dic = {}
    for row2 in rows2:
        dic[row2] = 0
    for row in rows:

        try:
            dic[row] += 1
        except:
            pass


    for k, v in dic.items():
        dic4['categories'].append(k)
        dic4['data'].append(v)
    return HttpResponse(json.dumps(dic4))



#退出功能
def logout(request):
    response = redirect("/login/")
    response.delete_cookie("name")
    response.delete_cookie("role")
    request.session.flush()
    return response


# ==================== 订单人界面 ====================
@customer_required
def customer_home(request):
    """订单人主页 - 浏览所有菜品"""
    user_name = request.get_signed_cookie("name")

    # 获取菜品类型参数
    food_type = request.GET.get('type', '全部')

    if food_type == "全部":
        result = Food.objects.all()
    else:
        result = Food.objects.filter(caipu__icontains=food_type)

    # 搜索功能
    word = request.GET.get("word")
    if word:
        if food_type == "全部":
            result = Food.objects.filter(title__icontains=word)
        else:
            result = Food.objects.filter(caipu__icontains=food_type).filter(title__icontains=word)

    # 分页
    paginator = Paginator(result, 12)
    page = request.GET.get('page')
    try:
        food_li = paginator.page(page)
    except PageNotAnInteger:
        food_li = paginator.page(1)
    except InvalidPage:
        return HttpResponse('找不到页面的内容')
    except EmptyPage:
        food_li = paginator.page(paginator.num_pages)

    return render(request, "customer/home.html", {
        "food_li": food_li,
        "user_name": user_name,
        "food_type": food_type,
        "word": word
    })


@customer_required
def customer_food_detail(request, food_id):
    """订单人查看菜品详情"""
    user_name = request.get_signed_cookie("name")
    try:
        food = Food.objects.get(id=food_id)
        # 处理配料分割
        ingredients = [ingredient.strip() for ingredient in food.peiliao.split(',') if ingredient.strip()]

        # 检查用户是否已经评分
        user = User.objects.get(name=user_name)
        existing_rating = FoodRating.objects.filter(food_id=food.food_id, user_id=user.id).first()

        return render(request, "customer/food_detail.html", {
            "food": food,
            "user_name": user_name,
            "ingredients": ingredients,
            "existing_rating": existing_rating
        })
    except Food.DoesNotExist:
        return HttpResponse('菜品不存在')
    except User.DoesNotExist:
        return HttpResponse('用户不存在')


@customer_required
def customer_recommendations(request):
    """订单人查看推荐菜品"""
    user_name = request.get_signed_cookie("name")
    user = User.objects.filter(name=user_name).first()
    types_interest = user.types_interest
    result = Food.objects.filter(caipu__icontains=types_interest)

    paginator = Paginator(result, 12)
    page = request.GET.get('page')
    try:
        food_li = paginator.page(page)
    except PageNotAnInteger:
        food_li = paginator.page(1)
    except InvalidPage:
        return HttpResponse('找不到页面的内容')
    except EmptyPage:
        food_li = paginator.page(paginator.num_pages)

    return render(request, "customer/recommendations.html", {
        "food_li": food_li,
        "user_name": user_name
    })


@customer_required
def customer_rate_food(request, food_id):
    """订单人为菜品评分"""
    if request.method == "POST":
        user_name = request.get_signed_cookie("name")
        rating = request.POST.get("rating")

        try:
            user = User.objects.get(name=user_name)
            food = Food.objects.get(id=food_id)

            # 检查是否已经评分
            existing_rating = FoodRating.objects.filter(food_id=food.food_id, user_id=user.id).first()

            if existing_rating:
                # 更新评分
                existing_rating.rating = rating
                existing_rating.save()
            else:
                # 创建新评分
                food_rating = FoodRating.create(food.food_id, food.title, user.id, rating)
                food_rating.save()

            return redirect(f"/customer/food/{food_id}/")

        except (User.DoesNotExist, Food.DoesNotExist):
            return HttpResponse('用户或菜品不存在')

    return redirect(f"/customer/food/{food_id}/")


# ==================== 商家界面 ====================
@merchant_required
def merchant_dashboard(request):
    """商家仪表板"""
    user_name = request.get_signed_cookie("name")

    # 统计数据
    total_foods = Food.objects.count()
    total_ratings = FoodRating.objects.count()

    # 获取最近添加的菜品
    recent_foods = Food.objects.all().order_by('-id')[:5]

    return render(request, "merchant/dashboard.html", {
        "user_name": user_name,
        "total_foods": total_foods,
        "total_ratings": total_ratings,
        "recent_foods": recent_foods
    })


@merchant_required
def merchant_food_list(request):
    """商家菜品管理列表"""
    user_name = request.get_signed_cookie("name")

    # 搜索功能
    word = request.GET.get("word")
    if word:
        result = Food.objects.filter(title__icontains=word)
    else:
        result = Food.objects.all()

    # 分页
    paginator = Paginator(result, 15)
    page = request.GET.get('page')
    try:
        food_li = paginator.page(page)
    except PageNotAnInteger:
        food_li = paginator.page(1)
    except InvalidPage:
        return HttpResponse('找不到页面的内容')
    except EmptyPage:
        food_li = paginator.page(paginator.num_pages)

    return render(request, "merchant/food_list.html", {
        "food_li": food_li,
        "user_name": user_name,
        "word": word
    })


@merchant_required
def merchant_food_add(request):
    """商家添加菜品"""
    user_name = request.get_signed_cookie("name")

    if request.method == "POST":
        title = request.POST.get("title")
        href = request.POST.get("href", "")
        peiliao = request.POST.get("peiliao")
        rate = request.POST.get("rate")
        food_id = request.POST.get("food_id")
        img = request.POST.get("img", "")
        caipu = request.POST.get("caipu")

        food = Food.create(title, href, peiliao, rate, food_id, img, caipu)
        food.save()

        return redirect("/merchant/foods/")

    return render(request, "merchant/food_add.html", {
        "user_name": user_name
    })


@merchant_required
def merchant_food_edit(request, food_id):
    """商家编辑菜品"""
    user_name = request.get_signed_cookie("name")

    try:
        food = Food.objects.get(id=food_id)
    except Food.DoesNotExist:
        return HttpResponse('菜品不存在')

    if request.method == "POST":
        food.title = request.POST.get("title")
        food.href = request.POST.get("href", "")
        food.peiliao = request.POST.get("peiliao")
        food.rate = request.POST.get("rate")
        food.food_id = request.POST.get("food_id")
        food.img = request.POST.get("img", "")
        food.caipu = request.POST.get("caipu")
        food.save()

        return redirect("/merchant/foods/")

    return render(request, "merchant/food_edit.html", {
        "food": food,
        "user_name": user_name
    })


@merchant_required
def merchant_food_delete(request, food_id):
    """商家删除菜品"""
    try:
        food = Food.objects.get(id=food_id)
        food.delete()
    except Food.DoesNotExist:
        pass

    return redirect("/merchant/foods/")


# 商家可视化图表视图
@merchant_or_admin_required
def merchant_chart_ingredients(request):
    """商家配料矩形图"""
    user_name = request.get_signed_cookie("name")
    return render(request, "merchant/chart_ingredients.html", {"user_name": user_name})


@merchant_or_admin_required
def merchant_chart_ingredients_data(request):
    """商家配料矩形图数据"""
    li = Food.objects.all()
    rows = []
    for i in li:
        rows.extend(i.peiliao.split(","))
    rows2 = list(set(rows))
    dic = {}
    for row2 in rows2:
        dic[row2] = 0
    for row in rows:
        try:
            dic[row] += 1
        except:
            pass
    result = []
    for k, v in dic.items():
        dic3 = {}
        dic3["name"] = f"{k}:{v}"
        dic3["value"] = v
        result.append(dic3)
    return HttpResponse(json.dumps(result))


@merchant_or_admin_required
def merchant_chart_ratings(request):
    """商家评分占比图"""
    user_name = request.get_signed_cookie("name")
    return render(request, "merchant/chart_ratings.html", {"user_name": user_name})


@merchant_or_admin_required
def merchant_chart_ratings_data(request):
    """商家评分占比图数据"""
    li = Food.objects.all()
    li2 = []
    for i in li:
        li2.append(i.caipu.strip("小吃"))
    li3 = list(set(li2))
    dic = {}
    for i in li3:
        dic[i] = 0
    for i in li2:
        try:
            dic[i] += 1
        except:
            pass
    result = []
    for k, v in dic.items():
        dic2 = {}
        dic2["name"] = k
        dic2["value"] = v
        result.append(dic2)
    return HttpResponse(json.dumps(result))


@merchant_or_admin_required
def merchant_chart_categories(request):
    """商家类别柱状图"""
    user_name = request.get_signed_cookie("name")
    return render(request, "merchant/chart_categories.html", {"user_name": user_name})


@merchant_or_admin_required
def merchant_chart_categories_data(request):
    """商家类别柱状图数据"""
    li = Food.objects.all()
    li2 = []
    for i in li:
        li2.append(i.caipu.strip("小吃"))
    li3 = list(set(li2))
    dic = {}
    for i in li3:
        dic[i] = 0
    for i in li2:
        try:
            dic[i] += 1
        except:
            pass
    dic2 = {}
    dic2["name"] = list(dic.keys())
    dic2["data"] = list(dic.values())
    return HttpResponse(json.dumps(dic2))


@merchant_or_admin_required
def merchant_chart_ingredients_bar(request):
    """商家配料柱状图"""
    user_name = request.get_signed_cookie("name")
    return render(request, "merchant/chart_ingredients_bar.html", {"user_name": user_name})


@merchant_or_admin_required
def merchant_chart_ingredients_bar_data(request):
    """商家配料柱状图数据"""
    li = Food.objects.all()
    rows = []
    for i in li:
        rows.extend(i.peiliao.split(","))
    rows2 = list(set(rows))
    dic = {}
    for row2 in rows2:
        dic[row2] = 0
    for row in rows:
        try:
            dic[row] += 1
        except:
            pass

    # 取前20个最常用的配料
    sorted_items = sorted(dic.items(), key=lambda x: x[1], reverse=True)[:20]

    dic4 = {}
    dic4["name"] = [item[0] for item in sorted_items]
    dic4["data"] = [item[1] for item in sorted_items]
    return HttpResponse(json.dumps(dic4))


# ==================== 管理员界面 ====================
@admin_required
def admin_dashboard(request):
    """管理员仪表板"""
    user_name = request.get_signed_cookie("name")

    # 统计数据
    total_foods = Food.objects.count()
    total_ratings = FoodRating.objects.count()
    total_users = User.objects.count()

    # 用户角色统计
    customer_count = User.objects.filter(role='customer').count()
    merchant_count = User.objects.filter(role='merchant').count()
    admin_count = User.objects.filter(role='admin').count()

    # 获取最近添加的菜品
    recent_foods = Food.objects.all().order_by('-id')[:5]

    return render(request, "admin/dashboard.html", {
        "user_name": user_name,
        "total_foods": total_foods,
        "total_ratings": total_ratings,
        "total_users": total_users,
        "customer_count": customer_count,
        "merchant_count": merchant_count,
        "admin_count": admin_count,
        "recent_foods": recent_foods
    })


@admin_required
def admin_model_weights(request):
    """管理员模型权重调整"""
    user_name = request.get_signed_cookie("name")

    # 默认权重值（可以从配置文件或数据库读取）
    default_weights = {
        'collaborative_filtering': 0.4,
        'content_based': 0.3,
        'popularity_based': 0.2,
        'hybrid_boost': 0.1
    }

    if request.method == "POST":
        # 获取用户提交的权重
        cf_weight = float(request.POST.get("cf_weight", 0.4))
        cb_weight = float(request.POST.get("cb_weight", 0.3))
        pb_weight = float(request.POST.get("pb_weight", 0.2))
        hb_weight = float(request.POST.get("hb_weight", 0.1))

        # 验证权重总和是否为1
        total_weight = cf_weight + cb_weight + pb_weight + hb_weight
        if abs(total_weight - 1.0) > 0.01:  # 允许小的浮点误差
            error_message = f"权重总和必须等于1.0，当前总和为{total_weight:.2f}"
            return render(request, "admin/model_weights.html", {
                "user_name": user_name,
                "weights": default_weights,
                "error_message": error_message
            })

        # 保存权重（这里可以保存到数据库或配置文件）
        updated_weights = {
            'collaborative_filtering': cf_weight,
            'content_based': cb_weight,
            'popularity_based': pb_weight,
            'hybrid_boost': hb_weight
        }

        success_message = "模型权重已成功更新！"
        return render(request, "admin/model_weights.html", {
            "user_name": user_name,
            "weights": updated_weights,
            "success_message": success_message
        })

    return render(request, "admin/model_weights.html", {
        "user_name": user_name,
        "weights": default_weights
    })


@admin_required
def admin_user_management(request):
    """管理员用户管理"""
    user_name = request.get_signed_cookie("name")

    # 搜索功能
    word = request.GET.get("word")
    role_filter = request.GET.get("role")

    users = User.objects.all()

    if word:
        users = users.filter(name__icontains=word)

    if role_filter:
        users = users.filter(role=role_filter)

    # 分页
    paginator = Paginator(users, 20)
    page = request.GET.get('page')
    try:
        user_li = paginator.page(page)
    except PageNotAnInteger:
        user_li = paginator.page(1)
    except InvalidPage:
        return HttpResponse('找不到页面的内容')
    except EmptyPage:
        user_li = paginator.page(paginator.num_pages)

    return render(request, "admin/user_management.html", {
        "user_li": user_li,
        "user_name": user_name,
        "word": word,
        "role_filter": role_filter
    })