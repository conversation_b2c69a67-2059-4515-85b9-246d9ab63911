#!/usr/bin/env python
"""
商家面板功能测试脚本
测试重新设计的商家面板所有功能
"""

import requests
import json
from urllib.parse import urljoin

class MerchantPanelTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_merchant_dashboard(self):
        """测试商家仪表板"""
        print("🏪 测试商家仪表板...")
        try:
            response = self.session.get(urljoin(self.base_url, "/merchant/dashboard/"))
            if response.status_code == 200:
                print("✅ 商家仪表板访问成功")
                return True
            else:
                print(f"❌ 商家仪表板访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 商家仪表板访问异常: {e}")
            return False
    
    def test_food_management(self):
        """测试菜品管理功能"""
        print("🍽️ 测试菜品管理功能...")
        pages = [
            "/merchant/foods/",
            "/merchant/foods/add/"
        ]
        
        success_count = 0
        for page in pages:
            try:
                url = urljoin(self.base_url, page)
                response = self.session.get(url)
                if response.status_code == 200:
                    print(f"✅ {page} 页面访问成功")
                    success_count += 1
                else:
                    print(f"❌ {page} 页面访问失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {page} 页面访问异常: {e}")
        
        print(f"📊 菜品管理测试结果: {success_count}/{len(pages)} 成功")
        return success_count == len(pages)
    
    def test_ratings_analysis(self):
        """测试评分分析功能"""
        print("⭐ 测试评分分析功能...")
        try:
            response = self.session.get(urljoin(self.base_url, "/merchant/ratings/"))
            if response.status_code == 200:
                print("✅ 评分分析页面访问成功")
                
                # 测试评分数据API
                data_response = self.session.get(urljoin(self.base_url, "/merchant/ratings/category-data/"))
                if data_response.status_code == 200:
                    try:
                        data = data_response.json()
                        print("✅ 评分数据API访问成功")
                        return True
                    except json.JSONDecodeError:
                        print("❌ 评分数据API返回格式错误")
                        return False
                else:
                    print(f"❌ 评分数据API访问失败，状态码: {data_response.status_code}")
                    return False
            else:
                print(f"❌ 评分分析页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 评分分析功能测试异常: {e}")
            return False
    
    def test_sales_analysis(self):
        """测试销售分析功能"""
        print("📈 测试销售分析功能...")
        try:
            response = self.session.get(urljoin(self.base_url, "/merchant/charts/sales/"))
            if response.status_code == 200:
                print("✅ 销售分析页面访问成功")
                return True
            else:
                print(f"❌ 销售分析页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 销售分析功能测试异常: {e}")
            return False
    
    def test_chart_pages(self):
        """测试图表页面"""
        print("📊 测试图表页面...")
        chart_pages = [
            "/merchant/charts/ingredients/",
            "/merchant/charts/categories/",
            "/merchant/charts/ingredients-bar/"
        ]
        
        success_count = 0
        for page in chart_pages:
            try:
                url = urljoin(self.base_url, page)
                response = self.session.get(url)
                if response.status_code == 200:
                    print(f"✅ {page} 图表页面访问成功")
                    success_count += 1
                else:
                    print(f"❌ {page} 图表页面访问失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {page} 图表页面访问异常: {e}")
        
        print(f"📊 图表页面测试结果: {success_count}/{len(chart_pages)} 成功")
        return success_count == len(chart_pages)
    
    def test_chart_data_apis(self):
        """测试图表数据API"""
        print("📊 测试图表数据API...")
        apis = [
            "/merchant/charts/ingredients/data/",
            "/merchant/charts/categories/data/",
            "/merchant/charts/ingredients-bar/data/"
        ]
        
        success_count = 0
        for api in apis:
            try:
                url = urljoin(self.base_url, api)
                response = self.session.get(url)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ {api} API访问成功，返回数据类型: {type(data)}")
                        success_count += 1
                    except json.JSONDecodeError:
                        print(f"❌ {api} API返回数据不是有效JSON")
                else:
                    print(f"❌ {api} API访问失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {api} API访问异常: {e}")
        
        print(f"📊 图表数据API测试结果: {success_count}/{len(apis)} 成功")
        return success_count == len(apis)
    
    def test_responsive_design(self):
        """测试响应式设计"""
        print("📱 测试响应式设计...")
        # 模拟不同设备的User-Agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",  # Desktop
            "Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15",  # Tablet
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15"  # Mobile
        ]
        
        success_count = 0
        for i, ua in enumerate(user_agents):
            device_type = ["桌面端", "平板端", "移动端"][i]
            try:
                headers = {"User-Agent": ua}
                response = self.session.get(urljoin(self.base_url, "/merchant/dashboard/"), headers=headers)
                if response.status_code == 200:
                    print(f"✅ {device_type}访问成功")
                    success_count += 1
                else:
                    print(f"❌ {device_type}访问失败")
            except Exception as e:
                print(f"❌ {device_type}访问异常: {e}")
        
        print(f"📱 响应式设计测试结果: {success_count}/{len(user_agents)} 成功")
        return success_count == len(user_agents)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始商家面板功能测试...")
        print("=" * 60)
        
        tests = [
            ("商家仪表板", self.test_merchant_dashboard),
            ("菜品管理", self.test_food_management),
            ("评分分析", self.test_ratings_analysis),
            ("销售分析", self.test_sales_analysis),
            ("图表页面", self.test_chart_pages),
            ("图表数据API", self.test_chart_data_apis),
            ("响应式设计", self.test_responsive_design)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 正在测试: {test_name}")
            print("-" * 40)
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        
        print("\n" + "=" * 60)
        print(f"🎯 商家面板测试总结: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！商家面板功能完整且运行正常。")
            print("✨ 新的商家面板特性:")
            print("   • 现代化的仪表板设计")
            print("   • 完整的菜品CRUD功能")
            print("   • 专业的评分分析系统")
            print("   • 详细的销售统计报表")
            print("   • 多种可视化图表")
            print("   • 响应式布局设计")
            print("   • 统一的设计风格")
        else:
            print("⚠️ 部分测试失败，请检查相关功能。")
        
        return passed == total

if __name__ == "__main__":
    tester = MerchantPanelTester()
    tester.run_all_tests()
