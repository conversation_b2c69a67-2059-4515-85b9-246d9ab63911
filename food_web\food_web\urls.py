"""food_web URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, re_path
from app01 import views
from django.views.generic import RedirectView  # 添加这行导入

urlpatterns = [
    # 重定向根路径到登录页面
    path('', RedirectView.as_view(url='/login/')),

    path('admin/', admin.site.urls),

    # 公共路由
    re_path(r'^login/', views.login),
    re_path(r'^reg/', views.reg),
    re_path(r'^logout/', views.logout),

    # ==================== 订单人路由 ====================
    re_path(r'^customer/home/', views.customer_home),
    re_path(r'^customer/food/(\d+)/$', views.customer_food_detail),
    re_path(r'^customer/food/(\d+)/rate/', views.customer_rate_food),
    re_path(r'^customer/food/(\d+)/comment/', views.customer_add_comment),
    re_path(r'^customer/recommendations/', views.customer_recommendations),

    # ==================== 商家路由 ====================
    re_path(r'^merchant/dashboard/', views.merchant_dashboard),
    re_path(r'^merchant/foods/$', views.merchant_food_list),
    re_path(r'^merchant/foods/add/', views.merchant_food_add),
    re_path(r'^merchant/foods/edit/(\d+)/', views.merchant_food_edit),
    re_path(r'^merchant/foods/delete/(\d+)/', views.merchant_food_delete),

    # 商家评论管理
    re_path(r'^merchant/comments/', views.merchant_comments),

    # 商家评分分析
    re_path(r'^merchant/ratings/', views.merchant_ratings_analysis),
    re_path(r'^merchant/ratings/category-data/', views.merchant_ratings_category_data),

    # 商家销售分析
    re_path(r'^merchant/charts/sales/', views.merchant_sales_analysis),

    # 商家可视化图表
    re_path(r'^merchant/charts/ingredients/', views.merchant_chart_ingredients),
    re_path(r'^merchant/charts/ingredients/data/', views.merchant_chart_ingredients_data),
    re_path(r'^merchant/charts/ratings/', views.merchant_chart_ratings),
    re_path(r'^merchant/charts/ratings/data/', views.merchant_chart_ratings_data),
    re_path(r'^merchant/charts/categories/', views.merchant_chart_categories),
    re_path(r'^merchant/charts/categories/data/', views.merchant_chart_categories_data),
    re_path(r'^merchant/charts/ingredients-bar/', views.merchant_chart_ingredients_bar),
    re_path(r'^merchant/charts/ingredients-bar/data/', views.merchant_chart_ingredients_bar_data),

    # ==================== 管理员路由 ====================
    re_path(r'^system-admin/dashboard/', views.admin_dashboard),
    re_path(r'^system-admin/model-weights/', views.admin_model_weights),
    re_path(r'^system-admin/users/', views.admin_user_management),

    # 管理员也可以访问所有商家功能
    re_path(r'^system-admin/foods/$', views.merchant_food_list),
    re_path(r'^system-admin/foods/add/', views.merchant_food_add),
    re_path(r'^system-admin/foods/edit/(\d+)/', views.merchant_food_edit),
    re_path(r'^system-admin/foods/delete/(\d+)/', views.merchant_food_delete),

    # 管理员可视化图表
    re_path(r'^system-admin/charts/ingredients/', views.merchant_chart_ingredients),
    re_path(r'^system-admin/charts/ingredients/data/', views.merchant_chart_ingredients_data),
    re_path(r'^system-admin/charts/ratings/', views.merchant_chart_ratings),
    re_path(r'^system-admin/charts/ratings/data/', views.merchant_chart_ratings_data),
    re_path(r'^system-admin/charts/categories/', views.merchant_chart_categories),
    re_path(r'^system-admin/charts/categories/data/', views.merchant_chart_categories_data),
    re_path(r'^system-admin/charts/ingredients-bar/', views.merchant_chart_ingredients_bar),
    re_path(r'^system-admin/charts/ingredients-bar/data/', views.merchant_chart_ingredients_bar_data),

    # ==================== 兼容旧路由（可选保留） ====================
    re_path(r'^home/(\w+)', views.home),
    re_path(r'^home_food_detail/(\d+)/', views.home_food_detail),
    re_path(r'^home_reco/', views.home_reco),
    re_path(r'^home_algorithm_reco/', views.home_algorithm_reco),
    re_path(r'^home2/', views.home2),
    re_path(r'^home2_data/', views.home2_data),
    re_path(r'^home3/', views.home3),
    re_path(r'^home3_data/', views.home3_data),
    re_path(r'^home4/', views.home4),
    re_path(r'^home4_data/', views.home4_data),
    re_path(r'^home5/', views.home5),
    re_path(r'^home5_data/', views.home5_data),
]
