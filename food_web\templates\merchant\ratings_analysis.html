{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>评分分析 - 中华美食网</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
        }
        .sidebar h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 18px;
            border-radius: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(5px);
            text-decoration: none;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .sidebar hr {
            border-color: #ff6b35;
            margin: 20px 0;
        }
        .sidebar h6 {
            color: #8b4513;
            font-weight: 600;
            margin: 20px 0 15px 0;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 10px;
            line-height: 1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .chart-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .chart-card h5 .icon {
            margin-right: 10px;
            color: #ff6b35;
            font-size: 1.3rem;
        }
        .chart-container {
            height: 400px;
            width: 100%;
        }
        .rating-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .rating-item:last-child {
            border-bottom: none;
        }
        .rating-stars {
            color: #ff6b35;
            font-size: 1.2rem;
            margin-right: 15px;
            min-width: 120px;
        }
        .rating-bar {
            flex: 1;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-right: 15px;
        }
        .rating-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-radius: 10px;
            transition: width 0.8s ease;
        }
        .rating-count {
            color: #8b4513;
            font-weight: 600;
            min-width: 60px;
            text-align: right;
        }
        .top-rated-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .food-rating-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        .food-rating-item:last-child {
            border-bottom: none;
        }
        .food-rating-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding-left: 10px;
            padding-right: 10px;
        }
        .food-rank {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .food-info {
            flex: 1;
        }
        .food-name {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .food-category {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .food-score {
            color: #ff6b35;
            font-weight: 600;
            font-size: 1.2rem;
        }
        .section-title {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }
        .section-title .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>⭐ 评分分析</h2>
                    <p class="mb-0">深入了解顾客评价 - {{ user_name }}（商家）</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">🏠 返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <h5>📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr>
                        <h6>📊 数据分析</h6>
                        <a class="nav-link active" href="/merchant/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/sales/">📈 销售统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📊 类别统计</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 评分统计卡片 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">⭐</div>
                            <div class="stats-number">{{ avg_rating|floatformat:1 }}</div>
                            <p class="stats-label">平均评分</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">📊</div>
                            <div class="stats-number">{{ total_ratings }}</div>
                            <p class="stats-label">总评价数</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">🔥</div>
                            <div class="stats-number">{{ high_rated_count }}</div>
                            <p class="stats-label">高分菜品</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">📈</div>
                            <div class="stats-number">{{ rating_trend }}%</div>
                            <p class="stats-label">评分趋势</p>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row">
                    <!-- 评分分布图 -->
                    <div class="col-lg-8">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">📊</span>
                                评分分布统计
                            </h5>
                            <div id="ratingDistributionChart" class="chart-container"></div>
                        </div>
                    </div>

                    <!-- 评分详细统计 -->
                    <div class="col-lg-4">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">⭐</span>
                                评分详情
                            </h5>
                            <div class="rating-item">
                                <div class="rating-stars">⭐⭐⭐⭐⭐ 5星</div>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: {{ five_star_percent }}%"></div>
                                </div>
                                <div class="rating-count">{{ five_star_count }}</div>
                            </div>
                            <div class="rating-item">
                                <div class="rating-stars">⭐⭐⭐⭐☆ 4星</div>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: {{ four_star_percent }}%"></div>
                                </div>
                                <div class="rating-count">{{ four_star_count }}</div>
                            </div>
                            <div class="rating-item">
                                <div class="rating-stars">⭐⭐⭐☆☆ 3星</div>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: {{ three_star_percent }}%"></div>
                                </div>
                                <div class="rating-count">{{ three_star_count }}</div>
                            </div>
                            <div class="rating-item">
                                <div class="rating-stars">⭐⭐☆☆☆ 2星</div>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: {{ two_star_percent }}%"></div>
                                </div>
                                <div class="rating-count">{{ two_star_count }}</div>
                            </div>
                            <div class="rating-item">
                                <div class="rating-stars">⭐☆☆☆☆ 1星</div>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: {{ one_star_percent }}%"></div>
                                </div>
                                <div class="rating-count">{{ one_star_count }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行图表 -->
                <div class="row">
                    <!-- 类别评分对比 -->
                    <div class="col-lg-8">
                        <div class="chart-card">
                            <h5>
                                <span class="icon">📈</span>
                                各类别平均评分
                            </h5>
                            <div id="categoryRatingChart" class="chart-container"></div>
                        </div>
                    </div>

                    <!-- 最高评分菜品 -->
                    <div class="col-lg-4">
                        <div class="top-rated-card">
                            <h5 style="color: #8b4513; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center;">
                                <span style="margin-right: 10px; color: #ff6b35;">🏆</span>
                                最高评分菜品
                            </h5>
                            {% for food in top_rated_foods %}
                            <div class="food-rating-item">
                                <div class="food-rank">{{ forloop.counter }}</div>
                                <div class="food-info">
                                    <div class="food-name">{{ food.title }}</div>
                                    <div class="food-category">{{ food.caipu }}</div>
                                </div>
                                <div class="food-score">{{ food.rate|floatformat:1 }}⭐</div>
                            </div>
                            {% empty %}
                            <div class="text-center text-muted py-4">
                                <p>暂无评分数据</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 评分分布图表
            var ratingChart = echarts.init(document.getElementById('ratingDistributionChart'));
            var ratingData = [
                { name: '5星', value: {{ five_star_count|default:0 }}, itemStyle: { color: '#ff6b35' } },
                { name: '4星', value: {{ four_star_count|default:0 }}, itemStyle: { color: '#f7931e' } },
                { name: '3星', value: {{ three_star_count|default:0 }}, itemStyle: { color: '#ffa726' } },
                { name: '2星', value: {{ two_star_count|default:0 }}, itemStyle: { color: '#ffb74d' } },
                { name: '1星', value: {{ one_star_count|default:0 }}, itemStyle: { color: '#ffcc02' } }
            ];

            var ratingOption = {
                title: {
                    text: '评分分布',
                    left: 'center',
                    textStyle: {
                        color: '#8b4513',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}个 ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom: '10%',
                    left: 'center'
                },
                series: [{
                    name: '评分分布',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '45%'],
                    data: ratingData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        formatter: '{b}\n{d}%'
                    }
                }]
            };
            ratingChart.setOption(ratingOption);

            // 类别评分对比图表
            var categoryChart = echarts.init(document.getElementById('categoryRatingChart'));

            // 获取类别评分数据
            $.get('/merchant/ratings/category-data/', function(data) {
                var categoryOption = {
                    title: {
                        text: '各类别平均评分对比',
                        left: 'center',
                        textStyle: {
                            color: '#8b4513',
                            fontSize: 16,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: '{b}: {c}分'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.categories,
                        axisLabel: {
                            rotate: 45,
                            color: '#8b4513',
                            fontSize: 12
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#ff6b35'
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '平均评分',
                        min: 0,
                        max: 5,
                        nameTextStyle: {
                            color: '#8b4513'
                        },
                        axisLabel: {
                            color: '#8b4513'
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#ff6b35'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#f0f0f0'
                            }
                        }
                    },
                    series: [{
                        name: '平均评分',
                        type: 'bar',
                        data: data.ratings,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#ff6b35'},
                                {offset: 1, color: '#f7931e'}
                            ]),
                            borderRadius: [4, 4, 0, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {offset: 0, color: '#ff8c42'},
                                    {offset: 1, color: '#ffa726'}
                                ])
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            color: '#8b4513',
                            fontWeight: 'bold',
                            formatter: '{c}分'
                        }
                    }]
                };
                categoryChart.setOption(categoryOption);
            }).fail(function() {
                // 如果没有数据，显示默认图表
                var defaultData = {
                    categories: ['北京小吃', '天津小吃', '山西小吃', '蒙古小吃', '山东小吃', '新疆小吃', '重庆小吃', '河南小吃'],
                    ratings: [4.2, 4.1, 4.3, 4.0, 4.4, 4.2, 4.5, 4.1]
                };

                var categoryOption = {
                    title: {
                        text: '各类别平均评分对比',
                        left: 'center',
                        textStyle: {
                            color: '#8b4513',
                            fontSize: 16,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: '{b}: {c}分'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: defaultData.categories,
                        axisLabel: {
                            rotate: 45,
                            color: '#8b4513',
                            fontSize: 12
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '平均评分',
                        min: 0,
                        max: 5
                    },
                    series: [{
                        name: '平均评分',
                        type: 'bar',
                        data: defaultData.ratings,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#ff6b35'},
                                {offset: 1, color: '#f7931e'}
                            ])
                        }
                    }]
                };
                categoryChart.setOption(categoryOption);
            });

            // 响应式调整
            window.addEventListener('resize', function() {
                ratingChart.resize();
                categoryChart.resize();
            });

            // 动画效果
            setTimeout(function() {
                $('.rating-fill').each(function() {
                    $(this).css('width', $(this).css('width'));
                });
            }, 500);
        });
    </script>
</body>
</html>
