{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>配料分析 - 商家管理中心</title>
    <style>
        .merchant-header {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(141, 157, 182, 0.1);
            margin-bottom: 20px;
        }
        .sidebar {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: #6c757d;
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🥘 配料分析</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #495057; margin-bottom: 20px;">📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #8d9db6;">
                        <h6 style="color: #495057; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link active" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="chart-container">
                    <h4 style="color: #495057; margin-bottom: 25px; text-align: center;">🥘 配料使用分布图</h4>
                    <div id="ingredientsChart" style="width: 100%; height: 500px;"></div>
                </div>
                
                <div class="chart-container">
                    <h5 style="color: #495057; margin-bottom: 15px;">📊 数据说明</h5>
                    <p class="text-muted">
                        此图表展示了所有菜品中各种配料的使用频率。图块大小代表该配料的使用次数，
                        颜色深浅表示使用频率的高低。这有助于您了解哪些配料最受欢迎，
                        从而优化采购和菜品搭配策略。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化ECharts图表
        var chart = echarts.init(document.getElementById('ingredientsChart'));
        
        // 获取数据并渲染图表
        $.get('/merchant/charts/ingredients/data/', function(data) {
            var option = {
                title: {
                    text: '配料使用分布',
                    left: 'center',
                    textStyle: {
                        color: '#495057',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}次'
                },
                series: [{
                    name: '配料使用次数',
                    type: 'treemap',
                    data: data,
                    roam: false,
                    nodeClick: false,
                    breadcrumb: {
                        show: false
                    },
                    label: {
                        show: true,
                        formatter: '{b}',
                        fontSize: 12
                    },
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 2,
                        gapWidth: 2
                    },
                    levels: [{
                        itemStyle: {
                            borderColor: '#8d9db6',
                            borderWidth: 3,
                            gapWidth: 3
                        }
                    }]
                }]
            };
            
            chart.setOption(option);
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>
