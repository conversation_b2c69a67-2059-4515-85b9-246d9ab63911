{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>精选美食 - 中华美食网</title>
    <style>
        .customer-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 15px 0;
            margin-bottom: 20px;
        }
        .recommendation-intro {
            background: linear-gradient(135deg, #fff8f0 0%, #ffeee0 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid #ff6b35;
        }
        .food-card {
            border: 1px solid #ffeee0;
            border-radius: 10px;
            margin-bottom: 20px;
            transition: transform 0.3s, box-shadow 0.3s;
            background: white;
            position: relative;
            overflow: hidden;
        }
        .food-card::before {
            content: "🌟 推荐";
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1;
        }
        .food-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
        }
        .food-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px 10px 0 0;
        }
        .food-info {
            padding: 15px;
        }
        .food-title {
            color: #8b4513;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .food-rating {
            color: #ff6b35;
            font-size: 14px;
        }
        .recommendation-badge {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="customer-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🍽️ 精选美食</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（尊贵顾客）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/customer/home/" class="btn btn-outline-light mr-2">返回首页</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 推荐说明 -->
        <div class="recommendation-intro">
            <div class="recommendation-badge">🎯 精选美食</div>
            <h3 style="color: #8b4513; margin-bottom: 15px;">为您精心挑选的美食</h3>
            <p style="color: #8b4513; margin-bottom: 0;">
                根据您的口味偏好，我们为您精选了以下优质美食。这些美食都是经过精心挑选的热门佳肴。
            </p>
        </div>

        <!-- 推荐菜品列表 -->
        <div class="row">
            {% for food in food_li %}
            <div class="col-md-4 col-sm-6">
                <div class="food-card">
                    <img src="{{ food.img }}" alt="{{ food.title }}" class="food-image" 
                         onerror="this.src='{% static 'images/default-food.jpg' %}'">
                    <div class="food-info">
                        <h5 class="food-title">{{ food.title }}</h5>
                        <p class="food-rating">⭐ 评分: {{ food.rate }}</p>
                        <p class="text-muted">{{ food.caipu }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="/customer/food/{{ food.id }}/" class="btn btn-sm btn-primary" 
                               style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                                查看详情
                            </a>
                            <small class="text-muted">💡 精选推荐</small>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="text-center" style="padding: 50px;">
                    <h4 style="color: #8b4513;">暂无推荐内容</h4>
                    <p class="text-muted">请先完善您的兴趣偏好，或浏览更多美食来获得更好的推荐</p>
                    <a href="/customer/home/" class="btn btn-primary" 
                       style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                        浏览所有美食
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if food_li.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if food_li.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ food_li.previous_page_number }}">上一页</a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">{{ food_li.number }} / {{ food_li.paginator.num_pages }}</span>
                </li>
                
                {% if food_li.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ food_li.next_page_number }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        <!-- 推荐说明 -->
        <div class="mt-5 p-4" style="background: #fff8f0; border-radius: 10px; border-left: 4px solid #ff6b35;">
            <h5 style="color: #8b4513;">🍽️ 美食精选说明</h5>
            <p class="text-muted mb-0">
                我们的美食精选基于用户评价和口味偏好，结合专业美食评鉴师的建议，
                为您推荐最受欢迎和最符合您口味的优质美食。
            </p>
        </div>
    </div>
</body>
</html>
