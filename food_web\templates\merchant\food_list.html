{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>菜品管理 - 中华美食网</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
        }
        .sidebar h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 18px;
            border-radius: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(5px);
            text-decoration: none;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .sidebar hr {
            border-color: #ff6b35;
            margin: 20px 0;
        }
        .sidebar h6 {
            color: #8b4513;
            font-weight: 600;
            margin: 20px 0 15px 0;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .search-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .food-table-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
        }
        .table-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 20px 25px;
            margin: 0;
        }
        .table-header h5 {
            margin: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        .table-header .icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        .food-table {
            margin: 0;
        }
        .food-table th {
            background-color: #f8f9fa;
            color: #8b4513;
            font-weight: 600;
            border: none;
            padding: 15px;
            font-size: 0.9rem;
        }
        .food-table td {
            padding: 15px;
            vertical-align: middle;
            border-color: #f0f0f0;
        }
        .food-table tbody tr {
            transition: all 0.3s ease;
        }
        .food-table tbody tr:hover {
            background-color: #fff8f0;
            transform: scale(1.01);
        }
        .food-image-cell {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
        }
        .food-title {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .food-category {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .food-rating {
            color: #ff6b35;
            font-weight: 600;
            font-size: 1.1rem;
        }
        .btn-action {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 500;
            margin: 0 2px;
            transition: all 0.3s ease;
        }
        .btn-edit {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .btn-delete {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
        }
        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
            color: white;
        }
        .btn-add {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-add:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
            color: white;
        }
        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .search-input:focus {
            border-color: #ff6b35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        .btn-search {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 500;
        }
        .btn-search:hover {
            color: white;
            transform: translateY(-2px);
        }
        .pagination .page-link {
            color: #8b4513;
            border: 1px solid #e9ecef;
            padding: 10px 15px;
            margin: 0 2px;
            border-radius: 8px;
        }
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-color: #ff6b35;
            color: white;
        }
        .pagination .page-link:hover {
            color: white;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border-color: #ff6b35;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state .icon {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }
        .stats-row {
            background: white;
            border-radius: 15px;
            padding: 20px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stats-info {
            color: #8b4513;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>🍽️ 菜品管理</h2>
                    <p class="mb-0">管理您的美食菜单 - {{ user_name }}（商家）</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">🏠 返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <h5>📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link active" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr>
                        <h6>📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/sales/">📈 销售统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📊 类别统计</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 统计信息 -->
                <div class="stats-row">
                    <div class="stats-info">
                        📊 共有 <strong>{{ food_li.paginator.count }}</strong> 个菜品
                        {% if word %}，搜索"<strong>{{ word }}</strong>"的结果{% endif %}
                    </div>
                    <div>
                        <a href="/merchant/foods/add/" class="btn btn-add">
                            ➕ 添加新菜品
                        </a>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="search-card">
                    <form method="get" class="row align-items-center">
                        <div class="col-md-8">
                            <input type="text" name="word" class="form-control search-input" 
                                   placeholder="🔍 搜索菜品名称..." value="{{ word }}">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-search w-100">搜索</button>
                        </div>
                        <div class="col-md-2">
                            <a href="/merchant/foods/" class="btn btn-secondary w-100">重置</a>
                        </div>
                    </form>
                </div>

                <!-- 菜品表格 -->
                <div class="food-table-card">
                    <div class="table-header">
                        <h5>
                            <span class="icon">🍽️</span>
                            菜品列表
                        </h5>
                    </div>
                    
                    {% if food_li %}
                    <table class="table food-table">
                        <thead>
                            <tr>
                                <th style="width: 100px;">图片</th>
                                <th>菜品信息</th>
                                <th style="width: 120px;">类别</th>
                                <th style="width: 100px;">评分</th>
                                <th style="width: 200px;">主要配料</th>
                                <th style="width: 150px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for food in food_li %}
                            <tr>
                                <td>
                                    <div class="food-image-cell">
                                        {{ food.title|first }}
                                    </div>
                                </td>
                                <td>
                                    <div class="food-title">{{ food.title }}</div>
                                    <small class="text-muted">ID: {{ food.food_id }}</small>
                                </td>
                                <td>
                                    <span class="food-category">{{ food.caipu }}</span>
                                </td>
                                <td>
                                    <div class="food-rating">{{ food.rate|floatformat:1 }}⭐</div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% for ingredient in food.peiliao|slice:":30" %}{{ ingredient }}{% endfor %}
                                        {% if food.peiliao|length > 30 %}...{% endif %}
                                    </small>
                                </td>
                                <td>
                                    <a href="/merchant/foods/edit/{{ food.id }}/" class="btn btn-action btn-edit">
                                        ✏️ 编辑
                                    </a>
                                    <a href="/merchant/foods/delete/{{ food.id }}/" class="btn btn-action btn-delete"
                                       onclick="return confirm('确定要删除菜品「{{ food.title }}」吗？')">
                                        🗑️ 删除
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="empty-state">
                        <div class="icon">🍽️</div>
                        <h5>暂无菜品</h5>
                        <p class="text-muted">
                            {% if word %}
                                没有找到包含"{{ word }}"的菜品
                            {% else %}
                                您还没有添加任何菜品
                            {% endif %}
                        </p>
                        <a href="/merchant/foods/add/" class="btn btn-add">添加第一个菜品</a>
                    </div>
                    {% endif %}
                </div>

                <!-- 分页 -->
                {% if food_li.has_other_pages %}
                <nav aria-label="Page navigation" class="d-flex justify-content-center">
                    <ul class="pagination">
                        {% if food_li.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ food_li.previous_page_number }}{% if word %}&word={{ word }}{% endif %}">
                                ← 上一页
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                第 {{ food_li.number }} 页 / 共 {{ food_li.paginator.num_pages }} 页
                            </span>
                        </li>
                        
                        {% if food_li.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ food_li.next_page_number }}{% if word %}&word={{ word }}{% endif %}">
                                下一页 →
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
