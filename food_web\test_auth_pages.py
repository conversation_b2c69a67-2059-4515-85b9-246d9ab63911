#!/usr/bin/env python
"""
登录和注册页面一致性测试脚本
验证两个页面的设计风格是否保持一致
"""

import requests
from bs4 import BeautifulSoup
import re

class AuthPagesConsistencyTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def get_page_content(self, path):
        """获取页面内容"""
        try:
            response = self.session.get(f"{self.base_url}{path}")
            if response.status_code == 200:
                return response.text
            else:
                print(f"❌ 无法访问 {path}，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 访问 {path} 时发生异常: {e}")
            return None
    
    def extract_css_properties(self, html_content):
        """提取CSS属性"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取style标签中的CSS
        style_tags = soup.find_all('style')
        css_content = ""
        for style in style_tags:
            css_content += style.get_text()
        
        # 提取关键的设计元素
        properties = {
            'background_gradient': [],
            'border_radius': [],
            'color_scheme': [],
            'font_family': [],
            'button_styles': [],
            'form_styles': []
        }
        
        # 提取背景渐变
        gradient_matches = re.findall(r'linear-gradient\([^)]+\)', css_content)
        properties['background_gradient'] = gradient_matches
        
        # 提取边框圆角
        radius_matches = re.findall(r'border-radius:\s*(\d+px)', css_content)
        properties['border_radius'] = radius_matches
        
        # 提取颜色方案
        color_matches = re.findall(r'#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}', css_content)
        properties['color_scheme'] = list(set(color_matches))
        
        # 提取字体
        font_matches = re.findall(r'font-family:\s*([^;]+)', css_content)
        properties['font_family'] = font_matches
        
        return properties
    
    def extract_layout_structure(self, html_content):
        """提取布局结构"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        structure = {
            'container_classes': [],
            'form_elements': [],
            'button_elements': [],
            'role_selection': False,
            'responsive_design': False
        }
        
        # 检查容器类
        containers = soup.find_all(['div'], class_=re.compile(r'container|login|register'))
        structure['container_classes'] = [div.get('class') for div in containers if div.get('class')]
        
        # 检查表单元素
        form_inputs = soup.find_all('input')
        structure['form_elements'] = [inp.get('type') for inp in form_inputs if inp.get('type')]
        
        # 检查按钮
        buttons = soup.find_all(['button', 'input'], type='submit')
        structure['button_elements'] = len(buttons)
        
        # 检查角色选择
        role_options = soup.find_all(['div'], class_=re.compile(r'role'))
        structure['role_selection'] = len(role_options) > 0
        
        # 检查响应式设计
        media_queries = re.findall(r'@media[^{]+{[^}]+}', str(soup))
        structure['responsive_design'] = len(media_queries) > 0
        
        return structure
    
    def compare_design_consistency(self):
        """比较设计一致性"""
        print("🎨 开始检查登录和注册页面设计一致性...")
        print("=" * 60)
        
        # 获取页面内容
        login_content = self.get_page_content("/login/")
        register_content = self.get_page_content("/reg/")
        
        if not login_content or not register_content:
            print("❌ 无法获取页面内容，测试终止")
            return False
        
        # 提取设计属性
        login_css = self.extract_css_properties(login_content)
        register_css = self.extract_css_properties(register_content)
        
        login_layout = self.extract_layout_structure(login_content)
        register_layout = self.extract_layout_structure(register_content)
        
        print("📊 设计一致性检查结果:")
        print("-" * 40)
        
        consistency_score = 0
        total_checks = 0
        
        # 检查背景渐变
        total_checks += 1
        if login_css['background_gradient'] and register_css['background_gradient']:
            if any(gradient in register_css['background_gradient'] for gradient in login_css['background_gradient']):
                print("✅ 背景渐变风格一致")
                consistency_score += 1
            else:
                print("❌ 背景渐变风格不一致")
        else:
            print("⚠️ 部分页面缺少背景渐变")
        
        # 检查颜色方案
        total_checks += 1
        common_colors = set(login_css['color_scheme']) & set(register_css['color_scheme'])
        if len(common_colors) >= 3:  # 至少3个共同颜色
            print(f"✅ 颜色方案一致 (共同颜色: {len(common_colors)}个)")
            consistency_score += 1
        else:
            print(f"❌ 颜色方案不够一致 (共同颜色: {len(common_colors)}个)")
        
        # 检查边框圆角
        total_checks += 1
        if login_css['border_radius'] and register_css['border_radius']:
            common_radius = set(login_css['border_radius']) & set(register_css['border_radius'])
            if common_radius:
                print("✅ 边框圆角风格一致")
                consistency_score += 1
            else:
                print("❌ 边框圆角风格不一致")
        else:
            print("⚠️ 部分页面缺少边框圆角设置")
        
        # 检查字体
        total_checks += 1
        if login_css['font_family'] and register_css['font_family']:
            if any('Microsoft YaHei' in font for font in login_css['font_family'] + register_css['font_family']):
                print("✅ 字体设置一致")
                consistency_score += 1
            else:
                print("❌ 字体设置不一致")
        else:
            print("⚠️ 字体设置不完整")
        
        # 检查布局结构
        total_checks += 1
        if (login_layout['role_selection'] and register_layout['role_selection'] and
            login_layout['button_elements'] > 0 and register_layout['button_elements'] > 0):
            print("✅ 布局结构一致")
            consistency_score += 1
        else:
            print("❌ 布局结构不一致")
        
        # 检查响应式设计
        total_checks += 1
        if login_layout['responsive_design'] and register_layout['responsive_design']:
            print("✅ 响应式设计一致")
            consistency_score += 1
        else:
            print("❌ 响应式设计不一致")
        
        print("\n" + "=" * 60)
        print(f"🎯 设计一致性评分: {consistency_score}/{total_checks} ({consistency_score/total_checks*100:.1f}%)")
        
        if consistency_score >= total_checks * 0.8:  # 80%以上一致性
            print("🎉 登录和注册页面设计风格高度一致！")
            print("✨ 主要一致性特征:")
            print("   • 相同的背景渐变色彩")
            print("   • 统一的颜色方案")
            print("   • 一致的边框圆角设计")
            print("   • 相同的字体选择")
            print("   • 统一的布局结构")
            print("   • 完整的响应式设计")
            return True
        else:
            print("⚠️ 页面设计一致性需要改进")
            return False
    
    def test_page_functionality(self):
        """测试页面功能"""
        print("\n🔧 测试页面基本功能...")
        print("-" * 40)
        
        # 测试登录页面
        login_content = self.get_page_content("/login/")
        if login_content and "欢迎登录" in login_content:
            print("✅ 登录页面加载正常")
        else:
            print("❌ 登录页面加载异常")
        
        # 测试注册页面
        register_content = self.get_page_content("/reg/")
        if register_content and "创建账号" in register_content:
            print("✅ 注册页面加载正常")
        else:
            print("❌ 注册页面加载异常")
        
        # 检查表单元素
        if login_content and register_content:
            login_soup = BeautifulSoup(login_content, 'html.parser')
            register_soup = BeautifulSoup(register_content, 'html.parser')
            
            login_inputs = len(login_soup.find_all('input'))
            register_inputs = len(register_soup.find_all('input'))
            
            print(f"📝 登录页面表单元素: {login_inputs}个")
            print(f"📝 注册页面表单元素: {register_inputs}个")
            
            if login_inputs >= 3 and register_inputs >= 5:  # 登录至少3个，注册至少5个
                print("✅ 表单元素数量合理")
            else:
                print("⚠️ 表单元素数量可能不足")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始登录注册页面一致性测试...")
        
        # 测试设计一致性
        design_consistent = self.compare_design_consistency()
        
        # 测试页面功能
        self.test_page_functionality()
        
        print("\n" + "=" * 60)
        if design_consistent:
            print("🎊 测试完成！登录和注册页面设计风格保持高度一致。")
        else:
            print("📝 测试完成！建议进一步优化页面设计一致性。")
        
        return design_consistent

if __name__ == "__main__":
    tester = AuthPagesConsistencyTester()
    tester.run_all_tests()
