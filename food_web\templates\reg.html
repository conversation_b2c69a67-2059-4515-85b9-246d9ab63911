{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美食智能推荐系统 - 注册</title>
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <style>
        body {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 25%, #5a6c7d 50%, #495057 75%, #6c757d 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        .register-left {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
        }
        .register-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        .register-right {
            padding: 60px 40px;
        }
        .system-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .system-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        .feature-list {
            text-align: left;
            margin-top: 30px;
        }
        .feature-item {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        .form-title {
            color: #495057;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #8d9db6;
            box-shadow: 0 0 0 0.2rem rgba(141, 157, 182, 0.25);
        }
        .role-selection {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        .role-option {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        .role-option:hover {
            background: #e9ecef;
        }
        .role-option.selected {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            border-color: #495057;
        }
        .role-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        .btn-register {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(141, 157, 182, 0.3);
            color: white;
        }
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        .login-link a {
            color: #8d9db6;
            text-decoration: none;
            font-weight: bold;
        }
        .login-link a:hover {
            color: #667292;
            text-decoration: underline;
        }
        .interest-help {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
        @media (max-width: 768px) {
            .register-left {
                display: none;
            }
            .register-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="row no-gutters h-100">
            <!-- 左侧介绍区域 -->
            <div class="col-md-6 register-left">
                <div style="position: relative; z-index: 1;">
                    <div class="system-title">🍽️ 美食智能推荐系统</div>
                    <div class="system-subtitle">加入我们，开启美食之旅</div>

                    <div class="feature-list">
                        <div class="feature-item">
                            <span class="feature-icon">🎯</span>
                            <span>个性化美食推荐</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🍜</span>
                            <span>中华地方小吃大全</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">⭐</span>
                            <span>真实用户评价</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📱</span>
                            <span>多端同步体验</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🏪</span>
                            <span>商家服务支持</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧注册表单 -->
            <div class="col-md-6 register-right">
                <h2 class="form-title">创建账号</h2>

                <form action="/reg/" method="post" id="registerForm">
                    {% csrf_token %}
                    <div class="form-group">
                        <input type="text" class="form-control" name="name" placeholder="请输入用户名" required>
                    </div>

                    <div class="form-group">
                        <input type="password" class="form-control" name="password" placeholder="请输入密码" required>
                    </div>

                    <div class="form-group">
                        <input type="number" class="form-control" name="age" placeholder="请输入年龄" min="1" max="120" required>
                    </div>

                    <div class="form-group">
                        <input type="text" class="form-control" name="types_interest" placeholder="喜欢的地方小吃类型" required>
                        <div class="interest-help">
                            例如：北京小吃、天津小吃、山西小吃等
                        </div>
                    </div>

                    <div class="role-selection">
                        <h6 style="color: #495057; margin-bottom: 15px;">选择用户角色：</h6>

                        <div class="role-option selected" onclick="selectRole('customer')">
                            <span class="role-icon">👤</span>
                            <div>
                                <strong>订单人</strong>
                                <div style="font-size: 0.9rem; opacity: 0.8;">浏览美食，获取推荐，参与评价</div>
                            </div>
                            <input type="radio" name="role" value="customer" checked style="display: none;">
                        </div>

                        <div class="role-option" onclick="selectRole('merchant')">
                            <span class="role-icon">🏪</span>
                            <div>
                                <strong>商家</strong>
                                <div style="font-size: 0.9rem; opacity: 0.8;">管理菜品，查看数据分析报表</div>
                            </div>
                            <input type="radio" name="role" value="merchant" style="display: none;">
                        </div>

                        <div class="role-option" onclick="selectRole('admin')">
                            <span class="role-icon">👑</span>
                            <div>
                                <strong>管理员</strong>
                                <div style="font-size: 0.9rem; opacity: 0.8;">系统管理，权重调整，用户管理</div>
                            </div>
                            <input type="radio" name="role" value="admin" style="display: none;">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-register">🎉 立即注册</button>
                </form>

                <div class="login-link">
                    <p>已有账号？<a href="/login/">立即登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectRole(role) {
            // 移除所有选中状态
            document.querySelectorAll('.role-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 添加选中状态
            event.currentTarget.classList.add('selected');

            // 设置对应的radio按钮
            document.querySelector(`input[value="${role}"]`).checked = true;
        }

        // 表单验证
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="name"]').value.trim();
            const password = document.querySelector('input[name="password"]').value;
            const age = document.querySelector('input[name="age"]').value;
            const interest = document.querySelector('input[name="types_interest"]').value.trim();

            if (name.length < 3) {
                alert('用户名至少需要3个字符');
                e.preventDefault();
                return;
            }

            if (password.length < 6) {
                alert('密码至少需要6个字符');
                e.preventDefault();
                return;
            }

            if (age < 1 || age > 120) {
                alert('请输入有效的年龄');
                e.preventDefault();
                return;
            }

            if (interest.length < 2) {
                alert('请输入您喜欢的地方小吃类型');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>