{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>美食推荐系统 - 订单人</title>
    <style>
        .customer-header {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            padding: 15px 0;
            margin-bottom: 20px;
        }
        .food-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            transition: transform 0.3s, box-shadow 0.3s;
            background: white;
        }
        .food-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(141, 157, 182, 0.2);
        }
        .food-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px 10px 0 0;
        }
        .food-info {
            padding: 15px;
        }
        .food-title {
            color: #495057;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .food-rating {
            color: #8d9db6;
            font-size: 14px;
        }
        .search-bar {
            background: white;
            border-radius: 25px;
            padding: 10px 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(141, 157, 182, 0.1);
        }
        .nav-tabs .nav-link {
            color: #6c757d;
            border: none;
            border-radius: 20px;
            margin-right: 10px;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="customer-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🍽️ 美食推荐系统</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（订单人）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/customer/recommendations/" class="btn btn-outline-light mr-2">个性推荐</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 搜索栏 -->
        <div class="search-bar">
            <form method="get" class="row align-items-center">
                <div class="col-md-8">
                    <input type="text" name="word" class="form-control border-0" 
                           placeholder="搜索您喜欢的美食..." value="{{ word }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary btn-block" 
                            style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                        🔍 搜索
                    </button>
                </div>
            </form>
        </div>

        <!-- 菜品分类导航 -->
        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link {% if food_type == '全部' %}active{% endif %}" 
                   href="?type=全部">全部美食</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if food_type == '川菜' %}active{% endif %}" 
                   href="?type=川菜">川菜</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if food_type == '粤菜' %}active{% endif %}" 
                   href="?type=粤菜">粤菜</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if food_type == '湘菜' %}active{% endif %}" 
                   href="?type=湘菜">湘菜</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if food_type == '小吃' %}active{% endif %}" 
                   href="?type=小吃">小吃</a>
            </li>
        </ul>

        <!-- 菜品列表 -->
        <div class="row">
            {% for food in food_li %}
            <div class="col-md-4 col-sm-6">
                <div class="food-card">
                    <img src="{{ food.img }}" alt="{{ food.title }}" class="food-image" 
                         onerror="this.src='{% static 'images/default-food.jpg' %}'">
                    <div class="food-info">
                        <h5 class="food-title">{{ food.title }}</h5>
                        <p class="food-rating">⭐ 评分: {{ food.rate }}</p>
                        <p class="text-muted">{{ food.caipu }}</p>
                        <a href="/customer/food/{{ food.id }}/" class="btn btn-sm btn-primary" 
                           style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                            查看详情
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <p class="text-muted">暂无相关美食，请尝试其他搜索条件</p>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if food_li.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if food_li.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ food_li.previous_page_number }}{% if word %}&word={{ word }}{% endif %}{% if food_type != '全部' %}&type={{ food_type }}{% endif %}">上一页</a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">{{ food_li.number }} / {{ food_li.paginator.num_pages }}</span>
                </li>
                
                {% if food_li.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ food_li.next_page_number }}{% if word %}&word={{ word }}{% endif %}{% if food_type != '全部' %}&type={{ food_type }}{% endif %}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</body>
</html>
