{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>添加菜品 - 商家管理中心</title>
    <style>
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1);
            margin-bottom: 20px;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .form-group label {
            color: #8b4513;
            font-weight: bold;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
        }
        .form-control:focus {
            border-color: #ff6b35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        .btn-submit {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
        }
        .btn-cancel {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🍽️ 商家管理中心</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #8b4513; margin-bottom: 20px;">📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link active" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #ff6b35;">
                        <h6 style="color: #8b4513; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="form-container">
                    <h3 style="color: #8b4513; margin-bottom: 30px;">➕ 添加新菜品</h3>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title">菜品名称 *</label>
                                    <input type="text" class="form-control" id="title" name="title" required 
                                           placeholder="请输入菜品名称">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="food_id">菜品ID *</label>
                                    <input type="text" class="form-control" id="food_id" name="food_id" required 
                                           placeholder="请输入唯一菜品ID">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="caipu">菜品类别 *</label>
                                    <select class="form-control" id="caipu" name="caipu" required>
                                        <option value="">请选择菜品类别</option>
                                        <option value="北京小吃">北京小吃</option>
                                        <option value="天津小吃">天津小吃</option>
                                        <option value="山西小吃">山西小吃</option>
                                        <option value="蒙古小吃">蒙古小吃</option>
                                        <option value="山东小吃">山东小吃</option>
                                        <option value="新疆小吃">新疆小吃</option>
                                        <option value="重庆小吃">重庆小吃</option>
                                        <option value="河南小吃">河南小吃</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="rate">评分 *</label>
                                    <input type="number" class="form-control" id="rate" name="rate" 
                                           min="0" max="5" step="0.1" required placeholder="0.0-5.0">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="img">菜品图片URL</label>
                            <input type="url" class="form-control" id="img" name="img" 
                                   placeholder="请输入菜品图片链接">
                        </div>

                        <div class="form-group">
                            <label for="href">详情链接</label>
                            <input type="url" class="form-control" id="href" name="href" 
                                   placeholder="请输入菜品详情页面链接">
                        </div>

                        <div class="form-group">
                            <label for="peiliao">主要配料 *</label>
                            <textarea class="form-control" id="peiliao" name="peiliao" rows="4" required 
                                      placeholder="请输入主要配料，用逗号分隔，例如：面粉,鸡蛋,葱花,盐"></textarea>
                        </div>

                        <div class="form-group text-right">
                            <a href="/merchant/foods/" class="btn btn-cancel mr-3">取消</a>
                            <button type="submit" class="btn btn-submit">保存菜品</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
