{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>添加菜品 - 商家管理中心</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .form-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
        }
        .form-container h3 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
        }
        .form-container h3 .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #f0f0f0;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(255, 107, 53, 0.3);
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .form-group label .required {
            color: #dc3545;
            margin-left: 5px;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #ff6b35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        .form-control::placeholder {
            color: #adb5bd;
        }
        .btn-submit {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 35px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .btn-cancel {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            padding: 15px 35px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .btn-cancel:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
            color: white;
        }
        .form-help {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #ff6b35;
        }
        .form-section h6 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .form-section h6 .icon {
            margin-right: 8px;
            color: #ff6b35;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>➕ 添加新菜品</h2>
                    <p class="mb-0">丰富您的菜单 - {{ user_name }}（商家）</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/merchant/foods/" class="btn btn-outline-light mr-2">🍽️ 菜品管理</a>
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">🏠 返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 主要功能区 -->
                    <div class="sidebar-section">
                        <h5>🏪 核心功能</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/merchant/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">仪表板</span>
                            </a>
                            <a class="nav-link" href="/merchant/foods/">
                                <span class="nav-icon">🍽️</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link active" href="/merchant/foods/add/">
                                <span class="nav-icon">➕</span>
                                <span class="nav-text">添加菜品</span>
                            </a>
                            <a class="nav-link" href="/merchant/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">用户评论</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 数据分析区 -->
                    <div class="sidebar-section">
                        <h5>📊 数据分析</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/merchant/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分分析</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/sales/">
                                <span class="nav-icon">📈</span>
                                <span class="nav-text">数据统计</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/ingredients/">
                                <span class="nav-icon">🥘</span>
                                <span class="nav-text">配料分析</span>
                            </a>
                            <a class="nav-link" href="/merchant/charts/categories/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">类别统计</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <div class="form-container">
                    <h3>
                        <span class="icon">➕</span>
                        添加新菜品
                    </h3>

                    <form method="post" id="addFoodForm">
                        {% csrf_token %}

                        <!-- 基本信息 -->
                        <div class="form-section">
                            <h6>
                                <span class="icon">📝</span>
                                基本信息
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title">
                                            菜品名称
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="title" name="title" required
                                               placeholder="请输入菜品名称，如：北京烤鸭">
                                        <div class="form-help">请输入具有吸引力的菜品名称</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="food_id">
                                            菜品编号
                                            <span class="required">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="food_id" name="food_id" required
                                               placeholder="请输入唯一菜品编号，如：BJ001">
                                        <div class="form-help">用于系统内部识别的唯一编号</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分类和评分 -->
                        <div class="form-section">
                            <h6>
                                <span class="icon">🏷️</span>
                                分类信息
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="caipu">
                                            菜品类别
                                            <span class="required">*</span>
                                        </label>
                                        <select class="form-control" id="caipu" name="caipu" required>
                                            <option value="">请选择菜品类别</option>
                                            <option value="北京小吃">🏮 北京小吃</option>
                                            <option value="天津小吃">🥟 天津小吃</option>
                                            <option value="山西小吃">🍜 山西小吃</option>
                                            <option value="蒙古小吃">🥩 蒙古小吃</option>
                                            <option value="山东小吃">🥞 山东小吃</option>
                                            <option value="新疆小吃">🍖 新疆小吃</option>
                                            <option value="重庆小吃">🌶️ 重庆小吃</option>
                                            <option value="河南小吃">🍲 河南小吃</option>
                                        </select>
                                        <div class="form-help">选择菜品所属的地方小吃类别</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="rate">
                                            初始评分
                                            <span class="required">*</span>
                                        </label>
                                        <input type="number" class="form-control" id="rate" name="rate"
                                               min="0" max="5" step="0.1" required placeholder="4.5" value="4.0">
                                        <div class="form-help">设置菜品的初始评分（0.0-5.0）</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 媒体信息 -->
                        <div class="form-section">
                            <h6>
                                <span class="icon">🖼️</span>
                                媒体信息
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="img">菜品图片链接</label>
                                        <input type="url" class="form-control" id="img" name="img"
                                               placeholder="https://example.com/food-image.jpg">
                                        <div class="form-help">可选：提供菜品的图片链接</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="href">详情页面链接</label>
                                        <input type="url" class="form-control" id="href" name="href"
                                               placeholder="https://example.com/food-detail">
                                        <div class="form-help">可选：菜品详细介绍页面链接</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 配料信息 -->
                        <div class="form-section">
                            <h6>
                                <span class="icon">🥘</span>
                                配料信息
                            </h6>
                            <div class="form-group">
                                <label for="peiliao">
                                    主要配料
                                    <span class="required">*</span>
                                </label>
                                <textarea class="form-control" id="peiliao" name="peiliao" rows="4" required
                                          placeholder="请输入主要配料，用逗号分隔，例如：面粉,鸡蛋,葱花,盐,胡椒粉"></textarea>
                                <div class="form-help">请用逗号分隔各种配料，这将用于配料分析</div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-group text-right">
                            <a href="/merchant/foods/" class="btn btn-cancel mr-3">
                                🚫 取消
                            </a>
                            <button type="submit" class="btn btn-submit">
                                💾 保存菜品
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 自动生成菜品编号
            $('#title').on('input', function() {
                var title = $(this).val();
                if (title && !$('#food_id').val()) {
                    var category = $('#caipu').val();
                    var prefix = '';
                    switch(category) {
                        case '北京小吃': prefix = 'BJ'; break;
                        case '天津小吃': prefix = 'TJ'; break;
                        case '山西小吃': prefix = 'SX'; break;
                        case '蒙古小吃': prefix = 'MG'; break;
                        case '山东小吃': prefix = 'SD'; break;
                        case '新疆小吃': prefix = 'XJ'; break;
                        case '重庆小吃': prefix = 'CQ'; break;
                        case '河南小吃': prefix = 'HN'; break;
                        default: prefix = 'FD'; break;
                    }
                    var timestamp = Date.now().toString().slice(-4);
                    $('#food_id').val(prefix + timestamp);
                }
            });

            // 类别改变时更新编号前缀
            $('#caipu').on('change', function() {
                var title = $('#title').val();
                if (title) {
                    $('#title').trigger('input');
                }
            });

            // 表单验证
            $('#addFoodForm').on('submit', function(e) {
                var isValid = true;
                var errors = [];

                // 检查必填字段
                if (!$('#title').val().trim()) {
                    errors.push('请输入菜品名称');
                    isValid = false;
                }

                if (!$('#food_id').val().trim()) {
                    errors.push('请输入菜品编号');
                    isValid = false;
                }

                if (!$('#caipu').val()) {
                    errors.push('请选择菜品类别');
                    isValid = false;
                }

                var rate = parseFloat($('#rate').val());
                if (isNaN(rate) || rate < 0 || rate > 5) {
                    errors.push('评分必须在0.0-5.0之间');
                    isValid = false;
                }

                if (!$('#peiliao').val().trim()) {
                    errors.push('请输入主要配料');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                    alert('请检查以下问题：\n' + errors.join('\n'));
                }
            });

            // 配料输入提示
            $('#peiliao').on('input', function() {
                var text = $(this).val();
                var ingredients = text.split(',').filter(item => item.trim());
                var count = ingredients.length;

                if (count > 0) {
                    $(this).next('.form-help').text(`已输入 ${count} 种配料，建议3-8种主要配料`);
                }
            });

            // 实时预览评分
            $('#rate').on('input', function() {
                var rate = parseFloat($(this).val());
                var stars = '';
                for (var i = 1; i <= 5; i++) {
                    if (i <= rate) {
                        stars += '⭐';
                    } else {
                        stars += '☆';
                    }
                }
                $(this).next('.form-help').text(`评分预览: ${stars} (${rate}分)`);
            });
        });
    </script>
</body>
</html>
