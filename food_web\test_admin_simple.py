#!/usr/bin/env python
"""
简化的管理员系统测试脚本
"""

import requests
from urllib.parse import urljoin

def test_admin_pages():
    base_url = "http://127.0.0.1:8000"
    
    admin_pages = {
        "管理员仪表板": "/system-admin/dashboard/",
        "用户管理": "/system-admin/users/",
        "模型权重调整": "/system-admin/model-weights/",
        "系统监控": "/system-admin/system-monitor/"
    }
    
    print("🚀 测试管理员页面访问...")
    print("=" * 50)
    
    working_pages = 0
    total_pages = len(admin_pages)
    
    for page_name, page_url in admin_pages.items():
        try:
            response = requests.get(urljoin(base_url, page_url), timeout=10)
            if response.status_code == 200:
                print(f"✅ {page_name}: 访问成功")
                working_pages += 1
            else:
                print(f"❌ {page_name}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"❌ {page_name}: 访问异常 - {e}")
    
    print("=" * 50)
    print(f"📊 测试结果: {working_pages}/{total_pages} 页面可访问")
    
    if working_pages == total_pages:
        print("🎉 所有管理员页面都可以正常访问！")
    elif working_pages >= total_pages * 0.75:
        print("👍 大部分管理员页面可以访问。")
    else:
        print("⚠️ 部分管理员页面需要检查。")
    
    return working_pages == total_pages

if __name__ == "__main__":
    test_admin_pages()
