# Generated by Django 5.2.3 on 2025-06-22 17:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0003_user_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='FoodComment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('food_id', models.CharField(max_length=1024)),
                ('food_name', models.Char<PERSON>ield(max_length=1024)),
                ('user_id', models.CharField(max_length=16)),
                ('user_name', models.Char<PERSON>ield(max_length=16)),
                ('comment', models.TextField()),
                ('rating', models.<PERSON>r<PERSON>ield(default='5', max_length=16)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_approved', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
