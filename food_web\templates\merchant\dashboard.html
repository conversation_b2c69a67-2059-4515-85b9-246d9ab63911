{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>商家管理中心 - 中华美食网</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .merchant-header {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .merchant-header h2 {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
            height: 100%;
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        }
        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 3rem;
            font-weight: 700;
            color: #8b4513;
            margin-bottom: 10px;
            line-height: 1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }
        .quick-action-card {
            background: white;
            border-radius: 15px;
            padding: 30px 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .quick-action-card:hover {
            transform: translateY(-8px);
            border-color: #ff6b35;
            box-shadow: 0 15px 35px rgba(255, 107, 53, 0.25);
            text-decoration: none;
        }
        .quick-action-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: #ff6b35;
        }
        .quick-action-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .quick-action-card p {
            color: #6c757d;
            margin: 0;
            font-size: 0.95rem;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
        }
        .sidebar h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        .sidebar .nav-link {
            color: #8b4513;
            padding: 12px 18px;
            border-radius: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            transform: translateX(5px);
            text-decoration: none;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border-color: #ff6b35;
        }
        .sidebar hr {
            border-color: #ff6b35;
            margin: 20px 0;
        }
        .sidebar h6 {
            color: #8b4513;
            font-weight: 600;
            margin: 20px 0 15px 0;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .chart-preview-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
        }
        .chart-preview-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .chart-preview-card h5 .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
        .recent-foods-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
            margin-bottom: 25px;
        }
        .recent-foods-card h5 {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .recent-foods-card h5 .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
        .food-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        .food-item:last-child {
            border-bottom: none;
        }
        .food-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding-left: 10px;
            padding-right: 10px;
        }
        .food-image {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
            font-size: 1.2rem;
        }
        .food-info {
            flex: 1;
        }
        .food-info h6 {
            margin: 0 0 5px 0;
            color: #8b4513;
            font-weight: 600;
        }
        .food-info small {
            color: #6c757d;
            display: block;
        }
        .food-rating {
            color: #ff6b35;
            font-weight: 600;
            font-size: 1.1rem;
        }
        .section-title {
            color: #8b4513;
            font-weight: 600;
            margin-bottom: 25px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }
        .section-title .icon {
            margin-right: 10px;
            color: #ff6b35;
        }
        .mini-chart {
            height: 200px;
            width: 100%;
        }
        .btn-view-more {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-view-more:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2>🏪 商家管理中心</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家） - 让美食传递温暖</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/customer/home/" class="btn btn-outline-light mr-2">🏠 返回首页</a>
                    <a href="/logout/" class="btn btn-outline-light">🚪 退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <h5>📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr>
                        <h6>📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/sales/">📈 销售统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📊 类别统计</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 统计卡片行 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">🍽️</div>
                            <div class="stats-number">{{ total_foods }}</div>
                            <p class="stats-label">总菜品数量</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">⭐</div>
                            <div class="stats-number">{{ total_ratings }}</div>
                            <p class="stats-label">总评价数量</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">📈</div>
                            <div class="stats-number">{{ avg_rating|floatformat:1 }}</div>
                            <p class="stats-label">平均评分</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon">🔥</div>
                            <div class="stats-number">{{ popular_foods_count }}</div>
                            <p class="stats-label">热门菜品</p>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作行 -->
                <h3 class="section-title">
                    <span class="icon">⚡</span>
                    快捷操作
                </h3>
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <a href="/merchant/foods/add/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">➕</div>
                                <h5>添加新菜品</h5>
                                <p>快速添加新的美食菜品到您的菜单</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="/merchant/foods/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">🍽️</div>
                                <h5>管理菜品</h5>
                                <p>编辑、删除和管理现有菜品信息</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="/merchant/ratings/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">⭐</div>
                                <h5>查看评价</h5>
                                <p>查看顾客对菜品的评价和反馈</p>
                            </div>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <a href="/merchant/charts/sales/" class="text-decoration-none">
                            <div class="quick-action-card">
                                <div class="quick-action-icon">📊</div>
                                <h5>数据分析</h5>
                                <p>查看销售数据和经营分析报表</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 数据概览行 -->
                <div class="row">
                    <!-- 最近菜品 -->
                    <div class="col-lg-6">
                        <div class="recent-foods-card">
                            <h5>
                                <span class="icon">🍽️</span>
                                最近添加的菜品
                            </h5>
                            {% for food in recent_foods %}
                            <div class="food-item">
                                <div class="food-image">
                                    {{ food.title|first }}
                                </div>
                                <div class="food-info">
                                    <h6>{{ food.title }}</h6>
                                    <small>{{ food.caipu }} • 评分: {{ food.rate|floatformat:1 }}⭐</small>
                                </div>
                                <div class="food-rating">
                                    ¥{{ food.rate|floatformat:0 }}
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center text-muted py-4">
                                <p>暂无菜品数据</p>
                                <a href="/merchant/foods/add/" class="btn btn-view-more">添加第一个菜品</a>
                            </div>
                            {% endfor %}
                            {% if recent_foods %}
                            <div class="text-center mt-3">
                                <a href="/merchant/foods/" class="btn btn-view-more">查看全部菜品</a>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 数据预览图表 -->
                    <div class="col-lg-6">
                        <div class="chart-preview-card">
                            <h5>
                                <span class="icon">📊</span>
                                菜品类别分布
                            </h5>
                            <div id="categoryChart" class="mini-chart"></div>
                            <div class="text-center mt-3">
                                <a href="/merchant/charts/categories/" class="btn btn-view-more">查看详细分析</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评分统计行 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="chart-preview-card">
                            <h5>
                                <span class="icon">⭐</span>
                                评分趋势
                            </h5>
                            <div id="ratingChart" class="mini-chart"></div>
                            <div class="text-center mt-3">
                                <a href="/merchant/ratings/" class="btn btn-view-more">查看评分详情</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="chart-preview-card">
                            <h5>
                                <span class="icon">🥘</span>
                                热门配料
                            </h5>
                            <div id="ingredientChart" class="mini-chart"></div>
                            <div class="text-center mt-3">
                                <a href="/merchant/charts/ingredients/" class="btn btn-view-more">查看配料分析</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 初始化类别分布图表
            var categoryChart = echarts.init(document.getElementById('categoryChart'));
            $.get('/merchant/charts/categories/data/', function(data) {
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: {c}个 ({d}%)'
                    },
                    series: [{
                        type: 'pie',
                        radius: ['30%', '70%'],
                        data: data.slice(0, 6), // 只显示前6个类别
                        itemStyle: {
                            borderRadius: 5,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    }]
                };
                categoryChart.setOption(option);
            });

            // 初始化评分趋势图表
            var ratingChart = echarts.init(document.getElementById('ratingChart'));
            var ratingData = [
                { name: '5星', value: {{ five_star_count|default:0 }} },
                { name: '4星', value: {{ four_star_count|default:0 }} },
                { name: '3星', value: {{ three_star_count|default:0 }} },
                { name: '2星', value: {{ two_star_count|default:0 }} },
                { name: '1星', value: {{ one_star_count|default:0 }} }
            ];
            var ratingOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '10%',
                    top: '10%'
                },
                xAxis: {
                    type: 'category',
                    data: ratingData.map(item => item.name),
                    axisLabel: {
                        fontSize: 10
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 10
                    }
                },
                series: [{
                    data: ratingData.map(item => item.value),
                    type: 'bar',
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#ff6b35'},
                            {offset: 1, color: '#f7931e'}
                        ]),
                        borderRadius: [4, 4, 0, 0]
                    }
                }]
            };
            ratingChart.setOption(ratingOption);

            // 初始化配料图表
            var ingredientChart = echarts.init(document.getElementById('ingredientChart'));
            $.get('/merchant/charts/ingredients-bar/data/', function(data) {
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    grid: {
                        left: '20%',
                        right: '10%',
                        bottom: '10%',
                        top: '10%'
                    },
                    xAxis: {
                        type: 'value',
                        axisLabel: {
                            fontSize: 10
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: data.name.slice(0, 8), // 只显示前8个配料
                        axisLabel: {
                            fontSize: 10
                        }
                    },
                    series: [{
                        data: data.data.slice(0, 8),
                        type: 'bar',
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                {offset: 0, color: '#ff6b35'},
                                {offset: 1, color: '#f7931e'}
                            ]),
                            borderRadius: [0, 4, 4, 0]
                        }
                    }]
                };
                ingredientChart.setOption(option);
            });

            // 响应式调整
            window.addEventListener('resize', function() {
                categoryChart.resize();
                ratingChart.resize();
                ingredientChart.resize();
            });
        });
    </script>
</body>
</html>
