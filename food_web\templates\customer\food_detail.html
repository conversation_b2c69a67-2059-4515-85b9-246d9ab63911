{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>{{ food.title }} - 中华美食网</title>
    <style>
        .customer-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .food-detail-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .food-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
        }
        .food-info {
            padding: 30px;
        }
        .food-title {
            color: #8b4513;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .food-rating {
            color: #ff6b35;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        .food-category {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 20px;
        }
        .ingredients-section {
            background: #fff8f0;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .ingredient-tag {
            background: #ffeee0;
            color: #8b4513;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            display: inline-block;
            border: 1px solid #ff6b35;
        }
        .back-btn {
            background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        .back-btn:hover {
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .rating-stars {
            font-size: 2rem;
            margin: 15px 0;
            text-align: center;
        }
        .star {
            cursor: pointer;
            color: #dee2e6;
            transition: all 0.3s ease;
            margin: 0 3px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            display: inline-block;
            transform: scale(1);
        }
        .star:hover {
            color: #ffc107;
            transform: scale(1.2);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .star.active {
            color: #ffc107;
            transform: scale(1.1);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .rating-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #8d9db6;
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            box-shadow: 0 5px 15px rgba(141, 157, 182, 0.1);
        }
        .rating-title {
            color: #495057;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        .current-rating {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
        }
        .rating-prompt {
            color: #6c757d;
            text-align: center;
            margin-bottom: 20px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="customer-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>🍽️ 美食详情</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（订单人）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/customer/home/" class="btn btn-outline-light mr-2">返回首页</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <a href="/customer/home/" class="back-btn">← 返回美食列表</a>
        
        <div class="food-detail-card">
            <div class="row no-gutters">
                <div class="col-md-6">
                    <img src="{{ food.img }}" alt="{{ food.title }}" class="food-image" 
                         onerror="this.src='{% static 'images/default-food.jpg' %}'">
                </div>
                <div class="col-md-6">
                    <div class="food-info">
                        <h1 class="food-title">{{ food.title }}</h1>
                        
                        <div class="food-rating">
                            ⭐ 评分: {{ food.rate }}
                        </div>
                        
                        <span class="food-category">{{ food.caipu }}</span>
                        
                        <div class="ingredients-section">
                            <h4 style="color: #8b4513; margin-bottom: 15px;">🥘 主要配料</h4>
                            {% for ingredient in ingredients %}
                            <span class="ingredient-tag">{{ ingredient }}</span>
                            {% endfor %}
                        </div>
                        
                        <div class="mt-4">
                            <h5 style="color: #8b4513;">📍 美食ID</h5>
                            <p class="text-muted">{{ food.food_id }}</p>
                        </div>
                        
                        <!-- 评分功能 -->
                        <div class="rating-section">
                            <h5 class="rating-title">⭐ 为这道菜评分</h5>

                            {% if existing_rating %}
                            <div class="current-rating">
                                <strong>您的评分：{{ existing_rating.rating }} 分</strong>
                                <div class="rating-stars mt-2">
                                    {% for i in "12345" %}
                                    <span class="star {% if forloop.counter <= existing_rating.rating|floatformat:0 %}active{% endif %}">⭐</span>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="text-center">
                                <button class="btn btn-outline-secondary" onclick="showRatingForm()">修改评分</button>
                            </div>
                            {% else %}
                            <p class="rating-prompt">您还没有为这道菜评分，快来分享您的体验吧！</p>
                            {% endif %}

                            <form method="post" action="/customer/food/{{ food.id }}/rate/" id="ratingForm"
                                  style="{% if existing_rating %}display: none;{% endif %} margin-top: 20px;">
                                <div class="text-center">
                                    <label class="form-label" style="color: #495057; font-weight: bold;">请选择评分：</label>
                                    <div class="rating-stars">
                                        {% for i in "12345" %}
                                        <span class="star" data-rating="{{ i }}" onclick="setRating({{ i }})">⭐</span>
                                        {% endfor %}
                                    </div>
                                    <input type="hidden" name="rating" id="rating-input" value="{% if existing_rating %}{{ existing_rating.rating }}{% else %}5{% endif %}">
                                    <div class="mt-3">
                                        <button type="submit" class="btn btn-primary btn-lg"
                                                style="background: linear-gradient(135deg, #8d9db6 0%, #667292 100%); border: none; padding: 12px 40px;">
                                            {% if existing_rating %}🔄 更新评分{% else %}✨ 提交评分{% endif %}
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        {% if food.href %}
                        <div class="mt-3">
                            <a href="{{ food.href }}" target="_blank" class="btn btn-primary"
                               style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none;">
                                🔗 查看原始链接
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 推荐相关美食 -->
        <div class="mt-5">
            <h3 style="color: #8b4513; text-align: center; margin-bottom: 30px;">🍴 您可能还喜欢</h3>
            <div class="text-center">
                <a href="/customer/recommendations/" class="btn btn-lg" 
                   style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); border: none; color: white;">
                    查看个性化推荐
                </a>
            </div>
        </div>
    </div>

    <script>
        function setRating(rating) {
            document.getElementById('rating-input').value = rating;

            // 更新星星显示
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        function showRatingForm() {
            document.getElementById('ratingForm').style.display = 'block';
        }

        // 页面加载时设置默认评分显示
        document.addEventListener('DOMContentLoaded', function() {
            const currentRating = document.getElementById('rating-input').value;
            if (currentRating) {
                setRating(parseInt(currentRating));
            }
        });
    </script>
</body>
</html>
