{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>评分分析 - 商家管理中心</title>
    <style>
        .merchant-header {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(141, 157, 182, 0.1);
            margin-bottom: 20px;
        }
        .sidebar {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: #6c757d;
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, #8d9db6 0%, #667292 100%);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="merchant-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>⭐ 评分分析</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（商家）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/merchant/dashboard/" class="btn btn-outline-light mr-2">返回仪表板</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #495057; margin-bottom: 20px;">📋 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/merchant/dashboard/">🏠 仪表板</a>
                        <a class="nav-link" href="/merchant/foods/">🍽️ 菜品管理</a>
                        <a class="nav-link" href="/merchant/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #8d9db6;">
                        <h6 style="color: #495057; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/merchant/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link active" href="/merchant/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/merchant/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/merchant/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <div class="chart-container">
                    <h4 style="color: #495057; margin-bottom: 25px; text-align: center;">⭐ 菜品类别分布</h4>
                    <div id="ratingsChart" style="width: 100%; height: 500px;"></div>
                </div>
                
                <div class="chart-container">
                    <h5 style="color: #495057; margin-bottom: 15px;">📊 数据说明</h5>
                    <p class="text-muted">
                        此饼图展示了不同菜品类别的数量分布。通过这个图表，您可以清楚地看到
                        各个菜系在您的菜品库中的占比情况，有助于平衡菜品种类，
                        满足不同顾客的口味需求。
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化ECharts图表
        var chart = echarts.init(document.getElementById('ratingsChart'));
        
        // 获取数据并渲染图表
        $.get('/merchant/charts/ratings/data/', function(data) {
            var option = {
                title: {
                    text: '菜品类别分布',
                    left: 'center',
                    textStyle: {
                        color: '#495057',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c}个 ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    top: 'middle'
                },
                series: [{
                    name: '菜品类别',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['60%', '50%'],
                    data: data,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    itemStyle: {
                        borderRadius: 5,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: true,
                        formatter: '{b}\n{d}%'
                    }
                }]
            };
            
            chart.setOption(option);
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>
