{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>用户管理 - 管理员控制台</title>
    <style>
        .admin-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .user-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.1);
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.1);
            margin-bottom: 20px;
        }
        .sidebar .nav-link {
            color: #6f42c1;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        .search-bar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.1);
        }
        .role-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .role-customer { background: #28a745; color: white; }
        .role-merchant { background: #ff6b35; color: white; }
        .role-admin { background: #6f42c1; color: white; }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>👥 用户管理</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（管理员）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/system-admin/dashboard/" class="btn btn-outline-light mr-2">返回控制台</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #6f42c1; margin-bottom: 20px;">🎛️ 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/system-admin/dashboard/">🏠 控制台</a>
                        <a class="nav-link active" href="/system-admin/users/">👥 用户管理</a>
                        <a class="nav-link" href="/system-admin/model-weights/">⚙️ 模型权重</a>
                        <hr style="border-color: #6c757d;">
                        <h6 style="color: #6c757d; margin: 15px 0 10px 0;">🍽️ 菜品管理</h6>
                        <a class="nav-link" href="/system-admin/foods/">📋 菜品列表</a>
                        <a class="nav-link" href="/system-admin/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #6c757d;">
                        <h6 style="color: #6c757d; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/system-admin/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/system-admin/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/system-admin/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/system-admin/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 搜索栏 -->
                <div class="search-bar">
                    <form method="get" class="row align-items-center">
                        <div class="col-md-4">
                            <input type="text" name="word" class="form-control" 
                                   placeholder="搜索用户名..." value="{{ word }}">
                        </div>
                        <div class="col-md-3">
                            <select name="role" class="form-control">
                                <option value="">所有角色</option>
                                <option value="customer" {% if role_filter == 'customer' %}selected{% endif %}>顾客</option>
                                <option value="merchant" {% if role_filter == 'merchant' %}selected{% endif %}>商家</option>
                                <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>管理员</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary" 
                                    style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); border: none;">
                                🔍 搜索
                            </button>
                        </div>
                        <div class="col-md-2">
                            <a href="/system-admin/users/" class="btn btn-secondary">重置</a>
                        </div>
                    </form>
                </div>

                <!-- 用户表格 -->
                <div class="user-table">
                    <table class="table table-hover mb-0">
                        <thead style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
                            <tr>
                                <th>用户</th>
                                <th>用户名</th>
                                <th>角色</th>
                                <th>兴趣偏好</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in user_li %}
                            <tr>
                                <td>
                                    <div class="user-avatar">
                                        {{ user.name|first|upper }}
                                    </div>
                                </td>
                                <td>
                                    <strong style="color: #6f42c1;">{{ user.name }}</strong>
                                    <br>
                                    <small class="text-muted">ID: {{ user.id }}</small>
                                </td>
                                <td>
                                    {% if user.role == 'customer' %}
                                        <span class="role-badge role-customer">顾客</span>
                                    {% elif user.role == 'merchant' %}
                                        <span class="role-badge role-merchant">商家</span>
                                    {% elif user.role == 'admin' %}
                                        <span class="role-badge role-admin">管理员</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% if user.types_interest %}
                                            {{ user.types_interest }}
                                        {% else %}
                                            未设置
                                        {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% if user.date_joined %}
                                            {{ user.date_joined|date:"Y-m-d" }}
                                        {% else %}
                                            未知
                                        {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewUser({{ user.id }})">
                                            👁️ 查看
                                        </button>
                                        {% if user.role != 'admin' %}
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="editUser({{ user.id }})">
                                            ✏️ 编辑
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <p class="text-muted">暂无用户数据</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if user_li.has_other_pages %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if user_li.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ user_li.previous_page_number }}{% if word %}&word={{ word }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}">上一页</a>
                        </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ user_li.number }} / {{ user_li.paginator.num_pages }}</span>
                        </li>
                        
                        {% if user_li.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ user_li.next_page_number }}{% if word %}&word={{ word }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}">下一页</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        function viewUser(userId) {
            alert('查看用户详情功能待开发 - 用户ID: ' + userId);
        }

        function editUser(userId) {
            alert('编辑用户功能待开发 - 用户ID: ' + userId);
        }
    </script>
</body>
</html>
