{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>用户管理 - 中华美食网</title>
    <style>
        body {
            background-color: #f4f6f9;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        .admin-header .content {
            position: relative;
            z-index: 1;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #e8ecf4;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #e8ecf4;
        }
        .sidebar .nav-link {
            color: #667eea;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .management-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        .management-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .management-card h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .management-card h5 .icon {
            margin-right: 10px;
            color: #764ba2;
            font-size: 1.3rem;
        }
        .user-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
        }
        .user-table table {
            margin: 0;
        }
        .user-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .user-table thead th {
            border: none;
            padding: 20px 15px;
            font-weight: 600;
            text-align: center;
        }
        .user-table tbody td {
            padding: 15px;
            vertical-align: middle;
            text-align: center;
            border-bottom: 1px solid #e8ecf4;
        }
        .user-table tbody tr:hover {
            background-color: #f8f9ff;
        }
        .role-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
        }
        .role-customer {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .role-merchant {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }
        .role-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-action {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 0.85rem;
            margin: 2px;
            border: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-edit {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
        }
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
            color: white;
        }
        .btn-delete {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            color: white;
        }
        .search-filter-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
        }
        .filter-group {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }
        .filter-item {
            flex: 1;
            min-width: 200px;
        }
        .filter-item label {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        .filter-item input,
        .filter-item select {
            border: 2px solid #e8ecf4;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        .filter-item input:focus,
        .filter-item select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        .btn-filter {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-filter:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        .stats-item {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
            border-top: 4px solid #667eea;
        }
        .stats-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .stats-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
            line-height: 1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
        }
        .pagination-wrapper {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
        }
        .pagination {
            margin: 0;
            justify-content: center;
        }
        .page-link {
            color: #667eea;
            border: 2px solid #e8ecf4;
            margin: 0 2px;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        .page-link:hover {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }
        .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2>👥 用户管理</h2>
                        <p class="mb-0">系统用户管理与权限控制 - {{ user_name }}（超级管理员）</p>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group">
                            <a href="/system-admin/dashboard/" class="btn btn-outline-light">
                                🏠 返回控制台
                            </a>
                            <a href="/logout/" class="btn btn-outline-light">
                                🚪 退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 系统管理区 -->
                    <div class="sidebar-section">
                        <h5>🎛️ 系统管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">控制台</span>
                            </a>
                            <a class="nav-link active" href="/system-admin/users/">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">用户管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/model-weights/">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">模型权重</span>
                            </a>
                            <a class="nav-link" href="/system-admin/system-monitor/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">系统监控</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 内容管理区 -->
                    <div class="sidebar-section">
                        <h5>🍽️ 内容管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/foods/">
                                <span class="nav-icon">📋</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">评论管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分管理</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 用户统计概览 -->
                <div class="stats-grid">
                    <div class="stats-item">
                        <div class="stats-icon">👥</div>
                        <div class="stats-number">{{ total_users|default:"0" }}</div>
                        <p class="stats-label">用户总数</p>
                    </div>
                    <div class="stats-item">
                        <div class="stats-icon">🛒</div>
                        <div class="stats-number">{{ customer_count|default:"0" }}</div>
                        <p class="stats-label">普通用户</p>
                    </div>
                    <div class="stats-item">
                        <div class="stats-icon">🏪</div>
                        <div class="stats-number">{{ merchant_count|default:"0" }}</div>
                        <p class="stats-label">商家用户</p>
                    </div>
                    <div class="stats-item">
                        <div class="stats-icon">👑</div>
                        <div class="stats-number">{{ admin_count|default:"0" }}</div>
                        <p class="stats-label">管理员</p>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="search-filter-card">
                    <h5 style="color: #667eea; font-weight: 600; margin-bottom: 20px; display: flex; align-items: center;">
                        <span style="margin-right: 10px; color: #764ba2; font-size: 1.3rem;">🔍</span>
                        搜索和筛选
                    </h5>
                    <form method="get" class="filter-group">
                        <div class="filter-item">
                            <label>用户名搜索</label>
                            <input type="text" name="word" class="form-control"
                                   placeholder="输入用户名..." value="{{ word }}">
                        </div>
                        <div class="filter-item">
                            <label>用户角色</label>
                            <select name="role" class="form-control">
                                <option value="">所有角色</option>
                                <option value="customer" {% if role_filter == 'customer' %}selected{% endif %}>普通用户</option>
                                <option value="merchant" {% if role_filter == 'merchant' %}selected{% endif %}>商家用户</option>
                                <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>管理员</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label>注册时间</label>
                            <select name="date_filter" class="form-control">
                                <option value="">所有时间</option>
                                <option value="today">今天</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                                <option value="year">本年</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 10px; align-items: end;">
                            <button type="submit" class="btn btn-filter">
                                🔍 搜索
                            </button>
                            <a href="/system-admin/users/" class="btn btn-secondary">
                                🔄 重置
                            </a>
                        </div>
                    </form>
                </div>

                <!-- 用户表格 -->
                <div class="user-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>头像</th>
                                <th>用户信息</th>
                                <th>角色</th>
                                <th>兴趣偏好</th>
                                <th>注册时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in user_li %}
                            <tr>
                                <td>
                                    <div class="user-avatar">
                                        {{ user.name|first|upper }}
                                    </div>
                                </td>
                                <td style="text-align: left;">
                                    <div>
                                        <strong style="color: #667eea; font-size: 1.1rem;">{{ user.name }}</strong>
                                        <br>
                                        <small class="text-muted">ID: {{ user.id }}</small>
                                        {% if user.email %}
                                        <br>
                                        <small class="text-muted">📧 {{ user.email }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if user.role == 'customer' %}
                                        <span class="role-badge role-customer">普通用户</span>
                                    {% elif user.role == 'merchant' %}
                                        <span class="role-badge role-merchant">商家用户</span>
                                    {% elif user.role == 'admin' %}
                                        <span class="role-badge role-admin">管理员</span>
                                    {% else %}
                                        <span class="role-badge" style="background: #6c757d; color: white;">未知</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div style="max-width: 150px;">
                                        {% if user.types_interest %}
                                            <span class="badge badge-light" style="background: #e8ecf4; color: #667eea;">
                                                {{ user.types_interest }}
                                            </span>
                                        {% else %}
                                            <small class="text-muted">未设置</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if user.date_joined %}
                                        <div style="color: #667eea; font-weight: 500;">
                                            {{ user.date_joined|date:"Y-m-d" }}
                                        </div>
                                        <small class="text-muted">
                                            {{ user.date_joined|date:"H:i" }}
                                        </small>
                                    {% else %}
                                        <small class="text-muted">未知</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge badge-success" style="background: #28a745; color: white; padding: 6px 12px; border-radius: 12px;">
                                            ✅ 活跃
                                        </span>
                                    {% else %}
                                        <span class="badge badge-secondary" style="background: #6c757d; color: white; padding: 6px 12px; border-radius: 12px;">
                                            ❌ 禁用
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-action btn-edit"
                                                onclick="viewUser({{ user.id }})" title="查看详情">
                                            👁️
                                        </button>
                                        {% if user.role != 'admin' %}
                                        <button type="button" class="btn btn-action btn-edit"
                                                onclick="editUser({{ user.id }})" title="编辑用户">
                                            ✏️
                                        </button>
                                        {% if user.is_active %}
                                        <button type="button" class="btn btn-action btn-delete"
                                                onclick="toggleUserStatus({{ user.id }}, false)" title="禁用用户">
                                            🚫
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-action btn-edit"
                                                onclick="toggleUserStatus({{ user.id }}, true)" title="启用用户">
                                            ✅
                                        </button>
                                        {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div style="color: #6c757d;">
                                        <div style="font-size: 3rem; margin-bottom: 15px;">👥</div>
                                        <h5>暂无用户数据</h5>
                                        <p class="mb-0">没有找到符合条件的用户</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if user_li.has_other_pages %}
                <div class="pagination-wrapper">
                    <nav aria-label="用户列表分页">
                        <ul class="pagination">
                            {% if user_li.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ user_li.previous_page_number }}{% if word %}&word={{ word }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}">
                                        ⬅️ 上一页
                                    </a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">{{ user_li.number }} / {{ user_li.paginator.num_pages }}</span>
                            </li>

                            {% if user_li.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ user_li.next_page_number }}{% if word %}&word={{ word }}{% endif %}{% if role_filter %}&role={{ role_filter }}{% endif %}">
                                        下一页 ➡️
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            显示第 {{ user_li.start_index|default:"1" }} - {{ user_li.end_index|default:"10" }} 条，共 {{ user_li.paginator.count|default:"0" }} 条记录
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div class="modal fade" id="userDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <h5 class="modal-title">👤 用户详情</h5>
                    <button type="button" class="close" data-dismiss="modal" style="color: white;">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="userDetailContent">
                    <!-- 用户详情内容将通过JavaScript加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function viewUser(userId) {
            // 显示用户详情模态框
            $('#userDetailContent').html(`
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载用户详情...</p>
                </div>
            `);
            $('#userDetailModal').modal('show');

            // 模拟加载用户详情
            setTimeout(function() {
                $('#userDetailContent').html(`
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="user-avatar" style="width: 100px; height: 100px; font-size: 2rem; margin: 0 auto 20px;">
                                U
                            </div>
                            <h5 style="color: #667eea;">用户 ${userId}</h5>
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>用户ID:</strong></td>
                                    <td>${userId}</td>
                                </tr>
                                <tr>
                                    <td><strong>用户名:</strong></td>
                                    <td>user_${userId}</td>
                                </tr>
                                <tr>
                                    <td><strong>角色:</strong></td>
                                    <td><span class="role-badge role-customer">普通用户</span></td>
                                </tr>
                                <tr>
                                    <td><strong>注册时间:</strong></td>
                                    <td>2024-01-01 10:00:00</td>
                                </tr>
                                <tr>
                                    <td><strong>最后登录:</strong></td>
                                    <td>2024-01-15 15:30:00</td>
                                </tr>
                                <tr>
                                    <td><strong>状态:</strong></td>
                                    <td><span class="badge badge-success">活跃</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                `);
            }, 1000);
        }

        function editUser(userId) {
            if (confirm('确定要编辑用户 ID: ' + userId + ' 吗？')) {
                // 这里可以跳转到编辑页面或显示编辑模态框
                alert('编辑功能开发中...');
            }
        }

        function toggleUserStatus(userId, enable) {
            var action = enable ? '启用' : '禁用';
            if (confirm('确定要' + action + '用户 ID: ' + userId + ' 吗？')) {
                // 这里可以发送AJAX请求来切换用户状态
                alert(action + '用户功能开发中...');
                // 成功后刷新页面
                // location.reload();
            }
        }

        // 页面加载完成后的初始化
        $(document).ready(function() {
            // 可以在这里添加其他初始化代码
            console.log('用户管理页面已加载');
        });
    </script>
</body>
</html>
