#!/usr/bin/env python
"""
最终改进验证测试脚本
验证所有商家页面的改进效果
"""

import requests
import json
from urllib.parse import urljoin

class FinalImprovementsTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_sidebar_consistency(self):
        """测试侧边栏一致性"""
        print("🔧 测试商家页面侧边栏一致性...")
        
        merchant_pages = [
            "/merchant/dashboard/",
            "/merchant/foods/",
            "/merchant/foods/add/",
            "/merchant/ratings/",
            "/merchant/charts/sales/",
            "/merchant/comments/"
        ]
        
        sidebar_elements = []
        for page in merchant_pages:
            try:
                response = self.session.get(urljoin(self.base_url, page))
                if response.status_code == 200:
                    # 检查是否包含新的侧边栏结构
                    if "sidebar-section" in response.text and "nav-icon" in response.text:
                        sidebar_elements.append(page)
                        print(f"✅ {page} 侧边栏结构已更新")
                    else:
                        print(f"❌ {page} 侧边栏结构未更新")
                else:
                    print(f"⚠️ {page} 无法访问")
            except Exception as e:
                print(f"❌ {page} 访问异常: {e}")
        
        consistency_rate = len(sidebar_elements) / len(merchant_pages) * 100
        print(f"📊 侧边栏一致性: {consistency_rate:.1f}%")
        return consistency_rate >= 80
    
    def test_comment_functionality(self):
        """测试评论功能"""
        print("\n💬 测试用户评论功能...")
        
        # 测试商家评论管理页面
        try:
            response = self.session.get(urljoin(self.base_url, "/merchant/comments/"))
            if response.status_code == 200:
                if "用户评论管理" in response.text and "comment-item" in response.text:
                    print("✅ 商家评论管理页面正常")
                    return True
                else:
                    print("❌ 商家评论管理页面功能不完整")
                    return False
            else:
                print(f"❌ 商家评论管理页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 评论功能测试异常: {e}")
            return False
    
    def test_data_visualization(self):
        """测试数据可视化改进"""
        print("\n📊 测试数据可视化改进...")
        
        # 测试销售分析页面
        try:
            response = self.session.get(urljoin(self.base_url, "/merchant/charts/sales/"))
            if response.status_code == 200:
                # 检查是否使用真实数据
                if "total_foods" in response.text and "total_ratings" in response.text:
                    print("✅ 销售分析页面使用真实数据")
                    
                    # 检查图表类型
                    if "ratingDistributionChart" in response.text and "userActivityChart" in response.text:
                        print("✅ 图表类型已更新为真实数据图表")
                        return True
                    else:
                        print("❌ 图表类型未完全更新")
                        return False
                else:
                    print("❌ 销售分析页面仍使用虚拟数据")
                    return False
            else:
                print(f"❌ 销售分析页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 数据可视化测试异常: {e}")
            return False
    
    def test_page_aesthetics(self):
        """测试页面美观性"""
        print("\n🎨 测试页面美观性...")
        
        aesthetic_features = {
            "现代化卡片设计": "stats-card",
            "渐变背景": "linear-gradient",
            "圆角设计": "border-radius",
            "阴影效果": "box-shadow",
            "响应式布局": "col-lg-",
            "图标系统": "nav-icon"
        }
        
        test_page = "/merchant/dashboard/"
        try:
            response = self.session.get(urljoin(self.base_url, test_page))
            if response.status_code == 200:
                passed_features = 0
                for feature_name, feature_code in aesthetic_features.items():
                    if feature_code in response.text:
                        print(f"✅ {feature_name} 已实现")
                        passed_features += 1
                    else:
                        print(f"❌ {feature_name} 未实现")
                
                aesthetic_score = passed_features / len(aesthetic_features) * 100
                print(f"📊 页面美观性评分: {aesthetic_score:.1f}%")
                return aesthetic_score >= 80
            else:
                print(f"❌ 测试页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 页面美观性测试异常: {e}")
            return False
    
    def test_navigation_consistency(self):
        """测试导航一致性"""
        print("\n🧭 测试导航一致性...")
        
        navigation_items = [
            "仪表板",
            "菜品管理", 
            "添加菜品",
            "用户评论",
            "评分分析",
            "数据统计",
            "配料分析",
            "类别统计"
        ]
        
        test_pages = [
            "/merchant/dashboard/",
            "/merchant/foods/",
            "/merchant/ratings/"
        ]
        
        consistent_pages = 0
        for page in test_pages:
            try:
                response = self.session.get(urljoin(self.base_url, page))
                if response.status_code == 200:
                    found_items = sum(1 for item in navigation_items if item in response.text)
                    if found_items >= len(navigation_items) * 0.8:  # 80%的导航项存在
                        print(f"✅ {page} 导航完整")
                        consistent_pages += 1
                    else:
                        print(f"❌ {page} 导航不完整 ({found_items}/{len(navigation_items)})")
                else:
                    print(f"⚠️ {page} 无法访问")
            except Exception as e:
                print(f"❌ {page} 导航测试异常: {e}")
        
        consistency_rate = consistent_pages / len(test_pages) * 100
        print(f"📊 导航一致性: {consistency_rate:.1f}%")
        return consistency_rate >= 80
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始商家页面最终改进验证测试...")
        print("=" * 70)
        
        tests = [
            ("侧边栏一致性", self.test_sidebar_consistency),
            ("评论功能", self.test_comment_functionality),
            ("数据可视化", self.test_data_visualization),
            ("页面美观性", self.test_page_aesthetics),
            ("导航一致性", self.test_navigation_consistency)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 正在测试: {test_name}")
            print("-" * 50)
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        
        print("\n" + "=" * 70)
        print(f"🎯 最终改进验证结果: {passed_tests}/{total_tests} 项测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有改进测试通过！商家页面已完全优化。")
            print("✨ 主要改进成果:")
            print("   • 🔧 左侧组件排列整齐规范")
            print("   • 💬 完整的用户评论功能")
            print("   • 📊 基于真实数据的可视化")
            print("   • 🎨 统一美观的页面设计")
            print("   • 🧭 一致的导航体验")
            print("   • 📱 完全响应式布局")
            print("   • 🏪 专业的商家管理界面")
        elif passed_tests >= total_tests * 0.8:
            print("👍 大部分改进测试通过，系统质量良好。")
        else:
            print("⚠️ 部分改进需要进一步优化。")
        
        return passed_tests == total_tests

if __name__ == "__main__":
    tester = FinalImprovementsTester()
    tester.run_comprehensive_test()
