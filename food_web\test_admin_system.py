#!/usr/bin/env python
"""
管理员系统功能验证测试脚本
验证管理员页面的全局管理功能和组件排列
"""

import requests
import json
from urllib.parse import urljoin

class AdminSystemTester:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_admin_dashboard(self):
        """测试管理员仪表板"""
        print("🏠 测试管理员仪表板...")
        
        try:
            response = self.session.get(urljoin(self.base_url, "/system-admin/dashboard/"))
            if response.status_code == 200:
                # 检查关键元素
                required_elements = [
                    "系统管理控制台",
                    "快速操作",
                    "系统运行状态",
                    "用户角色分布",
                    "系统数据趋势"
                ]
                
                found_elements = 0
                for element in required_elements:
                    if element in response.text:
                        found_elements += 1
                        print(f"✅ 找到元素: {element}")
                    else:
                        print(f"❌ 缺少元素: {element}")
                
                if found_elements >= len(required_elements) * 0.8:
                    print("✅ 管理员仪表板功能完整")
                    return True
                else:
                    print("❌ 管理员仪表板功能不完整")
                    return False
            else:
                print(f"❌ 管理员仪表板访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 管理员仪表板测试异常: {e}")
            return False
    
    def test_user_management(self):
        """测试用户管理功能"""
        print("\n👥 测试用户管理功能...")
        
        try:
            response = self.session.get(urljoin(self.base_url, "/system-admin/users/"))
            if response.status_code == 200:
                # 检查用户管理功能
                management_features = [
                    "用户管理",
                    "搜索和筛选",
                    "用户统计概览",
                    "用户表格",
                    "分页"
                ]
                
                found_features = 0
                for feature in management_features:
                    if feature in response.text:
                        found_features += 1
                        print(f"✅ 找到功能: {feature}")
                    else:
                        print(f"❌ 缺少功能: {feature}")
                
                # 检查组件排列
                layout_elements = [
                    "sidebar-section",
                    "stats-grid",
                    "search-filter-card",
                    "user-table"
                ]
                
                layout_score = 0
                for element in layout_elements:
                    if element in response.text:
                        layout_score += 1
                
                layout_quality = layout_score / len(layout_elements) * 100
                print(f"📊 组件排列质量: {layout_quality:.1f}%")
                
                if found_features >= len(management_features) * 0.8 and layout_quality >= 80:
                    print("✅ 用户管理功能完整且组件排列整齐")
                    return True
                else:
                    print("❌ 用户管理功能或组件排列需要改进")
                    return False
            else:
                print(f"❌ 用户管理页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 用户管理测试异常: {e}")
            return False
    
    def test_model_weights(self):
        """测试推荐模型权重调整功能"""
        print("\n⚙️ 测试推荐模型权重调整...")
        
        try:
            response = self.session.get(urljoin(self.base_url, "/system-admin/model-weights/"))
            if response.status_code == 200:
                # 检查权重调整功能
                weight_features = [
                    "推荐模型权重调整",
                    "混合推荐算法",
                    "协同过滤算法",
                    "内容过滤算法",
                    "热门度算法",
                    "地域偏好算法",
                    "权重总和检查"
                ]
                
                found_features = 0
                for feature in weight_features:
                    if feature in response.text:
                        found_features += 1
                        print(f"✅ 找到功能: {feature}")
                    else:
                        print(f"❌ 缺少功能: {feature}")
                
                # 检查交互元素
                interactive_elements = [
                    'input[type="range"]',
                    "weight-slider",
                    "weight-value",
                    "updateWeight"
                ]
                
                interactive_score = 0
                for element in interactive_elements:
                    if element in response.text:
                        interactive_score += 1
                
                interactive_quality = interactive_score / len(interactive_elements) * 100
                print(f"📊 交互功能完整度: {interactive_quality:.1f}%")
                
                if found_features >= len(weight_features) * 0.8 and interactive_quality >= 75:
                    print("✅ 推荐模型权重调整功能完整")
                    return True
                else:
                    print("❌ 推荐模型权重调整功能需要改进")
                    return False
            else:
                print(f"❌ 模型权重页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 模型权重测试异常: {e}")
            return False
    
    def test_system_monitor(self):
        """测试系统监控功能"""
        print("\n📊 测试系统监控功能...")
        
        try:
            response = self.session.get(urljoin(self.base_url, "/system-admin/system-monitor/"))
            if response.status_code == 200:
                # 检查监控功能
                monitor_features = [
                    "系统监控",
                    "系统状态概览",
                    "性能监控图表",
                    "系统日志",
                    "系统警报",
                    "服务器状态",
                    "数据库状态"
                ]
                
                found_features = 0
                for feature in monitor_features:
                    if feature in response.text:
                        found_features += 1
                        print(f"✅ 找到功能: {feature}")
                    else:
                        print(f"❌ 缺少功能: {feature}")
                
                # 检查图表和可视化
                chart_elements = [
                    "performanceChart",
                    "userActivityChart",
                    "echarts",
                    "chart-container"
                ]
                
                chart_score = 0
                for element in chart_elements:
                    if element in response.text:
                        chart_score += 1
                
                chart_quality = chart_score / len(chart_elements) * 100
                print(f"📊 可视化图表完整度: {chart_quality:.1f}%")
                
                if found_features >= len(monitor_features) * 0.8 and chart_quality >= 75:
                    print("✅ 系统监控功能完整")
                    return True
                else:
                    print("❌ 系统监控功能需要改进")
                    return False
            else:
                print(f"❌ 系统监控页面访问失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 系统监控测试异常: {e}")
            return False
    
    def test_layout_consistency(self):
        """测试页面布局一致性"""
        print("\n🎨 测试页面布局一致性...")
        
        admin_pages = [
            "/system-admin/dashboard/",
            "/system-admin/users/",
            "/system-admin/model-weights/",
            "/system-admin/system-monitor/"
        ]
        
        layout_elements = [
            "sidebar",
            "sidebar-section",
            "nav-link",
            "nav-icon",
            "nav-text",
            "admin-header"
        ]
        
        consistent_pages = 0
        for page in admin_pages:
            try:
                response = self.session.get(urljoin(self.base_url, page))
                if response.status_code == 200:
                    found_elements = sum(1 for element in layout_elements if element in response.text)
                    if found_elements >= len(layout_elements) * 0.8:
                        print(f"✅ {page} 布局一致")
                        consistent_pages += 1
                    else:
                        print(f"❌ {page} 布局不一致 ({found_elements}/{len(layout_elements)})")
                else:
                    print(f"⚠️ {page} 无法访问")
            except Exception as e:
                print(f"❌ {page} 测试异常: {e}")
        
        consistency_rate = consistent_pages / len(admin_pages) * 100
        print(f"📊 布局一致性: {consistency_rate:.1f}%")
        return consistency_rate >= 80
    
    def test_global_management_features(self):
        """测试全局管理功能"""
        print("\n🌐 测试全局管理功能...")
        
        global_features = {
            "用户管理": "/system-admin/users/",
            "权限控制": "/system-admin/model-weights/",
            "系统监控": "/system-admin/system-monitor/",
            "数据统计": "/system-admin/dashboard/"
        }
        
        working_features = 0
        for feature_name, feature_url in global_features.items():
            try:
                response = self.session.get(urljoin(self.base_url, feature_url))
                if response.status_code == 200:
                    print(f"✅ {feature_name} 功能可用")
                    working_features += 1
                else:
                    print(f"❌ {feature_name} 功能不可用")
            except Exception as e:
                print(f"❌ {feature_name} 测试异常: {e}")
        
        feature_coverage = working_features / len(global_features) * 100
        print(f"📊 全局管理功能覆盖率: {feature_coverage:.1f}%")
        return feature_coverage >= 80
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始管理员系统综合测试...")
        print("=" * 70)
        
        tests = [
            ("管理员仪表板", self.test_admin_dashboard),
            ("用户管理功能", self.test_user_management),
            ("模型权重调整", self.test_model_weights),
            ("系统监控功能", self.test_system_monitor),
            ("页面布局一致性", self.test_layout_consistency),
            ("全局管理功能", self.test_global_management_features)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 正在测试: {test_name}")
            print("-" * 50)
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        
        print("\n" + "=" * 70)
        print(f"🎯 管理员系统测试结果: {passed_tests}/{total_tests} 项测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有管理员功能测试通过！系统已完全优化。")
            print("✨ 主要成果:")
            print("   • 👑 完整的管理员控制台")
            print("   • 👥 全面的用户管理功能")
            print("   • ⚙️ 智能的推荐模型权重调整")
            print("   • 📊 实时的系统监控功能")
            print("   • 🎨 统一整齐的组件排列")
            print("   • 🌐 完善的全局管理权限")
            print("   • 📱 完全响应式的管理界面")
            print("   • 🔒 专业的权限控制系统")
        elif passed_tests >= total_tests * 0.8:
            print("👍 大部分管理员功能测试通过，系统质量良好。")
        else:
            print("⚠️ 部分管理员功能需要进一步优化。")
        
        return passed_tests == total_tests

if __name__ == "__main__":
    tester = AdminSystemTester()
    tester.run_comprehensive_test()
