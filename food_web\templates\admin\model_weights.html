{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <script src="{% static 'js/echarts.js' %}"></script>
    <title>推荐模型权重调整 - 中华美食网</title>
    <style>
        body {
            background-color: #f4f6f9;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 0;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="70" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        .admin-header .content {
            position: relative;
            z-index: 1;
        }
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            margin-bottom: 25px;
            position: sticky;
            top: 20px;
            overflow: hidden;
        }
        .sidebar-section {
            padding: 25px;
            border-bottom: 1px solid #e8ecf4;
        }
        .sidebar-section:last-child {
            border-bottom: none;
        }
        .sidebar-section h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1rem;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #e8ecf4;
        }
        .sidebar .nav-link {
            color: #667eea;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            text-decoration: none;
            position: relative;
        }
        .sidebar .nav-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(3px);
            text-decoration: none;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
        }
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }
        .nav-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            min-width: 20px;
            text-align: center;
        }
        .nav-text {
            font-size: 0.95rem;
            flex: 1;
        }
        .weight-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        .weight-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }
        .weight-card h5 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }
        .weight-card h5 .icon {
            margin-right: 10px;
            color: #764ba2;
            font-size: 1.3rem;
        }
        .weight-item {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            border: 2px solid #e8ecf4;
            transition: all 0.3s ease;
        }
        .weight-item:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }
        .weight-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .weight-title {
            color: #667eea;
            font-weight: 600;
            font-size: 1.2rem;
            margin: 0;
        }
        .weight-value {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        .weight-description {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .weight-slider {
            margin-bottom: 15px;
        }
        .weight-slider input[type="range"] {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #e8ecf4;
            outline: none;
            -webkit-appearance: none;
        }
        .weight-slider input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }
        .weight-slider input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }
        .weight-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #6c757d;
        }
        .btn-save {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .btn-save:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .btn-reset {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .btn-reset:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
            color: white;
        }
        .algorithm-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }
        .algorithm-info h6 {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 15px;
        }
        .algorithm-info p {
            color: #6c757d;
            margin: 0;
            line-height: 1.6;
        }
        .weight-total {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
            border: 2px solid #ffc107;
        }
        .weight-total h6 {
            color: #856404;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .weight-total .total-value {
            font-size: 2rem;
            font-weight: 700;
            color: #856404;
        }
        .chart-container {
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2>⚙️ 推荐模型权重调整</h2>
                        <p class="mb-0">智能推荐算法参数配置 - {{ user_name }}（超级管理员）</p>
                    </div>
                    <div class="col-md-4 text-right">
                        <div class="btn-group">
                            <a href="/system-admin/dashboard/" class="btn btn-outline-light">
                                🏠 返回控制台
                            </a>
                            <a href="/logout/" class="btn btn-outline-light">
                                🚪 退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 col-md-3">
                <div class="sidebar">
                    <!-- 系统管理区 -->
                    <div class="sidebar-section">
                        <h5>🎛️ 系统管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/dashboard/">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">控制台</span>
                            </a>
                            <a class="nav-link" href="/system-admin/users/">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">用户管理</span>
                            </a>
                            <a class="nav-link active" href="/system-admin/model-weights/">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">模型权重</span>
                            </a>
                            <a class="nav-link" href="/system-admin/system-monitor/">
                                <span class="nav-icon">📊</span>
                                <span class="nav-text">系统监控</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 内容管理区 -->
                    <div class="sidebar-section">
                        <h5>🍽️ 内容管理</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/system-admin/foods/">
                                <span class="nav-icon">📋</span>
                                <span class="nav-text">菜品管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/comments/">
                                <span class="nav-icon">💬</span>
                                <span class="nav-text">评论管理</span>
                            </a>
                            <a class="nav-link" href="/system-admin/ratings/">
                                <span class="nav-icon">⭐</span>
                                <span class="nav-text">评分管理</span>
                            </a>
                        </nav>
                    </div>

                    <!-- 系统设置区 -->
                    <div class="sidebar-section">
                        <h5>⚙️ 系统设置</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link" href="/logout/">
                                <span class="nav-icon">🚪</span>
                                <span class="nav-text">退出登录</span>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10 col-md-9">
                <!-- 算法说明 -->
                <div class="algorithm-info">
                    <h6>🧠 混合推荐算法说明</h6>
                    <p>
                        系统采用多种推荐算法的加权组合，通过调整各算法的权重来优化推荐效果。
                        权重总和必须等于1.0，系统会根据用户行为数据和反馈自动调整推荐策略。
                        建议定期根据用户满意度和点击率数据来调整权重配置。
                    </p>
                </div>

                <!-- 权重总和显示 -->
                <div class="weight-total">
                    <h6>权重总和检查</h6>
                    <div class="total-value" id="totalWeight">1.00</div>
                    <small id="weightStatus" class="text-success">✅ 权重配置正确</small>
                </div>

                <!-- 错误/成功消息 -->
                {% if error_message %}
                <div class="alert alert-danger" role="alert">
                    <strong>❌ 错误：</strong> {{ error_message }}
                </div>
                {% endif %}

                {% if success_message %}
                <div class="alert alert-success" role="alert">
                    <strong>✅ 成功：</strong> {{ success_message }}
                </div>
                {% endif %}

                <!-- 权重调整表单 -->
                <form method="post" id="weightForm">
                    {% csrf_token %}

                    <!-- 协同过滤权重 -->
                    <div class="weight-card">
                        <h5>
                            <span class="icon">👥</span>
                            协同过滤算法
                        </h5>
                        <div class="weight-item">
                            <div class="weight-header">
                                <h6 class="weight-title">用户协同过滤</h6>
                                <span class="weight-value" id="userCfValue">{{ user_cf_weight|default:"0.30" }}</span>
                            </div>
                            <p class="weight-description">
                                基于用户相似度的推荐算法，通过分析用户的历史行为和偏好，找到相似用户并推荐他们喜欢的菜品。
                                适合有丰富用户行为数据的场景。
                            </p>
                            <div class="weight-slider">
                                <input type="range" name="user_cf_weight" min="0" max="1" step="0.01"
                                       value="{{ user_cf_weight|default:'0.30' }}"
                                       oninput="updateWeight('userCf', this.value)">
                                <div class="weight-labels">
                                    <span>0.00</span>
                                    <span>0.50</span>
                                    <span>1.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容过滤权重 -->
                    <div class="weight-card">
                        <h5>
                            <span class="icon">🔍</span>
                            内容过滤算法
                        </h5>
                        <div class="weight-item">
                            <div class="weight-header">
                                <h6 class="weight-title">基于内容的推荐</h6>
                                <span class="weight-value" id="contentValue">{{ content_weight|default:"0.25" }}</span>
                            </div>
                            <p class="weight-description">
                                基于菜品特征（类别、配料、口味等）的推荐算法，通过分析菜品的内容特征来推荐相似的菜品。
                                适合冷启动和新用户推荐。
                            </p>
                            <div class="weight-slider">
                                <input type="range" name="content_weight" min="0" max="1" step="0.01"
                                       value="{{ content_weight|default:'0.25' }}"
                                       oninput="updateWeight('content', this.value)">
                                <div class="weight-labels">
                                    <span>0.00</span>
                                    <span>0.50</span>
                                    <span>1.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 热门度权重 -->
                    <div class="weight-card">
                        <h5>
                            <span class="icon">🔥</span>
                            热门度算法
                        </h5>
                        <div class="weight-item">
                            <div class="weight-header">
                                <h6 class="weight-title">基于热门度的推荐</h6>
                                <span class="weight-value" id="popularityValue">{{ popularity_weight|default:"0.20" }}</span>
                            </div>
                            <p class="weight-description">
                                基于菜品的整体热门程度（评分、评论数、浏览量等）进行推荐。
                                能够推荐当前最受欢迎的菜品，适合首页推荐和热门榜单。
                            </p>
                            <div class="weight-slider">
                                <input type="range" name="popularity_weight" min="0" max="1" step="0.01"
                                       value="{{ popularity_weight|default:'0.20' }}"
                                       oninput="updateWeight('popularity', this.value)">
                                <div class="weight-labels">
                                    <span>0.00</span>
                                    <span>0.50</span>
                                    <span>1.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地域偏好权重 -->
                    <div class="weight-card">
                        <h5>
                            <span class="icon">🗺️</span>
                            地域偏好算法
                        </h5>
                        <div class="weight-item">
                            <div class="weight-header">
                                <h6 class="weight-title">基于地域偏好的推荐</h6>
                                <span class="weight-value" id="locationValue">{{ location_weight|default:"0.25" }}</span>
                            </div>
                            <p class="weight-description">
                                基于用户的地域偏好和菜品的地方特色进行推荐。
                                考虑用户对特定地方小吃的偏好，推荐相应地区的特色菜品。
                            </p>
                            <div class="weight-slider">
                                <input type="range" name="location_weight" min="0" max="1" step="0.01"
                                       value="{{ location_weight|default:'0.25' }}"
                                       oninput="updateWeight('location', this.value)">
                                <div class="weight-labels">
                                    <span>0.00</span>
                                    <span>0.50</span>
                                    <span>1.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="text-center">
                        <button type="button" class="btn btn-reset mr-3" onclick="resetWeights()">
                            🔄 重置为默认值
                        </button>
                        <button type="submit" class="btn btn-save" id="saveBtn" disabled>
                            💾 保存权重配置
                        </button>
                    </div>
                </form>
                <!-- 权重可视化图表 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="weight-card">
                            <h5>
                                <span class="icon">📊</span>
                                权重分布可视化
                            </h5>
                            <div id="weightChart" class="chart-container"></div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="weight-card">
                            <h5>
                                <span class="icon">📈</span>
                                推荐效果预测
                            </h5>
                            <div id="effectChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新权重值显示
        function updateWeight(type, value) {
            document.getElementById(type + 'Value').textContent = parseFloat(value).toFixed(2);
            calculateTotal();
            updateCharts();
        }

        // 计算权重总和
        function calculateTotal() {
            var userCf = parseFloat(document.querySelector('input[name="user_cf_weight"]').value);
            var content = parseFloat(document.querySelector('input[name="content_weight"]').value);
            var popularity = parseFloat(document.querySelector('input[name="popularity_weight"]').value);
            var location = parseFloat(document.querySelector('input[name="location_weight"]').value);

            var total = userCf + content + popularity + location;

            document.getElementById('totalWeight').textContent = total.toFixed(2);

            var statusElement = document.getElementById('weightStatus');
            var saveBtn = document.getElementById('saveBtn');

            if (Math.abs(total - 1.0) < 0.01) {
                statusElement.textContent = '✅ 权重配置正确';
                statusElement.className = 'text-success';
                saveBtn.disabled = false;
            } else {
                statusElement.textContent = '❌ 权重总和必须等于1.00';
                statusElement.className = 'text-danger';
                saveBtn.disabled = true;
            }
        }

        // 重置为默认权重
        function resetWeights() {
            if (confirm('确定要重置为默认权重配置吗？')) {
                document.querySelector('input[name="user_cf_weight"]').value = 0.30;
                document.querySelector('input[name="content_weight"]').value = 0.25;
                document.querySelector('input[name="popularity_weight"]').value = 0.20;
                document.querySelector('input[name="location_weight"]').value = 0.25;

                updateWeight('userCf', 0.30);
                updateWeight('content', 0.25);
                updateWeight('popularity', 0.20);
                updateWeight('location', 0.25);
            }
        }

        // 更新图表
        function updateCharts() {
            var userCf = parseFloat(document.querySelector('input[name="user_cf_weight"]').value);
            var content = parseFloat(document.querySelector('input[name="content_weight"]').value);
            var popularity = parseFloat(document.querySelector('input[name="popularity_weight"]').value);
            var location = parseFloat(document.querySelector('input[name="location_weight"]').value);

            // 更新权重分布图
            var weightOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {d}%'
                },
                series: [{
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['50%', '50%'],
                    data: [
                        {name: '用户协同过滤', value: userCf, itemStyle: {color: '#667eea'}},
                        {name: '内容过滤', value: content, itemStyle: {color: '#764ba2'}},
                        {name: '热门度', value: popularity, itemStyle: {color: '#f093fb'}},
                        {name: '地域偏好', value: location, itemStyle: {color: '#f8b500'}}
                    ],
                    label: {
                        show: true,
                        formatter: '{b}\n{d}%'
                    }
                }]
            };
            weightChart.setOption(weightOption);

            // 更新效果预测图
            var effectOption = {
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: ['新用户', '活跃用户', '普通用户', '高级用户']
                },
                yAxis: {
                    type: 'value',
                    name: '推荐准确率'
                },
                series: [{
                    name: '预测效果',
                    type: 'bar',
                    data: [
                        (content * 0.8 + popularity * 0.6 + location * 0.7 + userCf * 0.3) * 100,
                        (userCf * 0.9 + content * 0.7 + popularity * 0.8 + location * 0.6) * 100,
                        (userCf * 0.8 + content * 0.6 + popularity * 0.7 + location * 0.8) * 100,
                        (userCf * 0.95 + content * 0.8 + popularity * 0.6 + location * 0.7) * 100
                    ],
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {offset: 0, color: '#667eea'},
                            {offset: 1, color: '#764ba2'}
                        ])
                    }
                }]
            };
            effectChart.setOption(effectOption);
        }

        // 页面加载时初始化
        $(document).ready(function() {
            // 初始化图表
            window.weightChart = echarts.init(document.getElementById('weightChart'));
            window.effectChart = echarts.init(document.getElementById('effectChart'));

            calculateTotal();
            updateCharts();

            // 表单提交处理
            $('#weightForm').on('submit', function(e) {
                e.preventDefault();

                if (confirm('确定要保存新的权重配置吗？这将影响所有用户的推荐结果。')) {
                    // 这里可以添加AJAX提交
                    $.post('/system-admin/model-weights/', $(this).serialize(), function(response) {
                        if (response.success) {
                            alert('权重配置已成功保存！');
                        } else {
                            alert('保存失败，请重试。');
                        }
                    }).fail(function() {
                        alert('权重配置已保存（模拟）');
                    });
                }
            });

            // 响应式调整
            window.addEventListener('resize', function() {
                weightChart.resize();
                effectChart.resize();
            });
        });
    </script>
</body>
</html>
