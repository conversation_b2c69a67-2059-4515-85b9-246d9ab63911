{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/bootstrap.min.js' %}"></script>
    <title>模型权重调整 - 管理员控制台</title>
    <style>
        .admin-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .weight-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.1);
            border-left: 5px solid #6f42c1;
        }
        .weight-slider {
            margin: 20px 0;
        }
        .weight-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #6f42c1;
        }
        .model-description {
            background: #f8f9ff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #6f42c1;
        }
        .sidebar {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .nav-link {
            color: #6f42c1;
            padding: 12px 20px;
            border-radius: 10px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        .nav-link:hover {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            text-decoration: none;
        }
        .nav-link.active {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        .total-weight {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        .total-weight.valid {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        .total-weight.invalid {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        .range-input {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        .range-input::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #6f42c1;
            cursor: pointer;
        }
        .range-input::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #6f42c1;
            cursor: pointer;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2>⚙️ 模型权重调整</h2>
                    <p class="mb-0">欢迎您，{{ user_name }}（管理员）</p>
                </div>
                <div class="col-md-6 text-right">
                    <a href="/system-admin/dashboard/" class="btn btn-outline-light mr-2">返回控制台</a>
                    <a href="/logout/" class="btn btn-outline-light">退出登录</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 style="color: #6f42c1; margin-bottom: 20px;">🎛️ 管理菜单</h5>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="/system-admin/dashboard/">🏠 控制台</a>
                        <a class="nav-link" href="/system-admin/users/">👥 用户管理</a>
                        <a class="nav-link active" href="/system-admin/model-weights/">⚙️ 模型权重</a>
                        <hr style="border-color: #6f42c1;">
                        <h6 style="color: #6f42c1; margin: 15px 0 10px 0;">🍽️ 菜品管理</h6>
                        <a class="nav-link" href="/system-admin/foods/">📋 菜品列表</a>
                        <a class="nav-link" href="/system-admin/foods/add/">➕ 添加菜品</a>
                        <hr style="border-color: #6f42c1;">
                        <h6 style="color: #6f42c1; margin: 15px 0 10px 0;">📊 数据分析</h6>
                        <a class="nav-link" href="/system-admin/charts/ingredients/">🥘 配料分析</a>
                        <a class="nav-link" href="/system-admin/charts/ratings/">⭐ 评分分析</a>
                        <a class="nav-link" href="/system-admin/charts/categories/">📈 类别统计</a>
                        <a class="nav-link" href="/system-admin/charts/ingredients-bar/">📊 配料排行</a>
                    </nav>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9">
                <!-- 说明信息 -->
                <div class="model-description">
                    <h4 style="color: #6f42c1;">⚙️ 推荐系统权重调整</h4>
                    <p class="mb-0">
                        调整不同推荐方式在系统中的权重。所有权重的总和必须等于1.0。
                        权重越高，该推荐方式在最终结果中的影响越大。
                    </p>
                </div>

                <!-- 错误/成功消息 -->
                {% if error_message %}
                <div class="alert alert-danger" role="alert">
                    <strong>❌ 错误：</strong> {{ error_message }}
                </div>
                {% endif %}

                {% if success_message %}
                <div class="alert alert-success" role="alert">
                    <strong>✅ 成功：</strong> {{ success_message }}
                </div>
                {% endif %}

                <!-- 权重调整表单 -->
                <form method="post" id="weightForm">
                    <div class="weight-card">
                        <h5 style="color: #6f42c1; margin-bottom: 25px;">🎯 推荐权重设置</h5>
                        
                        <!-- 协同过滤 -->
                        <div class="weight-slider">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label><strong>🤝 用户偏好推荐</strong></label>
                                <span class="weight-value" id="cf-value">{{ weights.collaborative_filtering }}</span>
                            </div>
                            <input type="range" class="range-input" name="cf_weight" id="cf-weight" 
                                   min="0" max="1" step="0.01" value="{{ weights.collaborative_filtering }}"
                                   onchange="updateWeight('cf')">
                            <small class="text-muted">根据用户喜好相似性进行推荐</small>
                        </div>

                        <!-- 基于内容 -->
                        <div class="weight-slider">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label><strong>📋 内容匹配推荐</strong></label>
                                <span class="weight-value" id="cb-value">{{ weights.content_based }}</span>
                            </div>
                            <input type="range" class="range-input" name="cb_weight" id="cb-weight" 
                                   min="0" max="1" step="0.01" value="{{ weights.content_based }}"
                                   onchange="updateWeight('cb')">
                            <small class="text-muted">基于菜品特征和用户偏好进行推荐</small>
                        </div>

                        <!-- 基于流行度 -->
                        <div class="weight-slider">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label><strong>🔥 热门美食推荐</strong></label>
                                <span class="weight-value" id="pb-value">{{ weights.popularity_based }}</span>
                            </div>
                            <input type="range" class="range-input" name="pb_weight" id="pb-weight" 
                                   min="0" max="1" step="0.01" value="{{ weights.popularity_based }}"
                                   onchange="updateWeight('pb')">
                            <small class="text-muted">推荐热门和高评分的菜品</small>
                        </div>

                        <!-- 混合增强 -->
                        <div class="weight-slider">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label><strong>⚡ 综合优化推荐</strong></label>
                                <span class="weight-value" id="hb-value">{{ weights.hybrid_boost }}</span>
                            </div>
                            <input type="range" class="range-input" name="hb_weight" id="hb-weight" 
                                   min="0" max="1" step="0.01" value="{{ weights.hybrid_boost }}"
                                   onchange="updateWeight('hb')">
                            <small class="text-muted">综合调整和优化推荐结果</small>
                        </div>

                        <!-- 权重总和显示 -->
                        <div class="total-weight" id="total-weight">
                            权重总和: <span id="total-value">1.00</span>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg" id="submit-btn"
                                    style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); border: none;">
                                💾 保存权重设置
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ml-3" onclick="resetWeights()">
                                🔄 重置为默认值
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function updateWeight(type) {
            const value = document.getElementById(type + '-weight').value;
            document.getElementById(type + '-value').textContent = parseFloat(value).toFixed(2);
            updateTotalWeight();
        }

        function updateTotalWeight() {
            const cf = parseFloat(document.getElementById('cf-weight').value);
            const cb = parseFloat(document.getElementById('cb-weight').value);
            const pb = parseFloat(document.getElementById('pb-weight').value);
            const hb = parseFloat(document.getElementById('hb-weight').value);
            
            const total = cf + cb + pb + hb;
            const totalElement = document.getElementById('total-weight');
            const totalValue = document.getElementById('total-value');
            const submitBtn = document.getElementById('submit-btn');
            
            totalValue.textContent = total.toFixed(2);
            
            if (Math.abs(total - 1.0) < 0.01) {
                totalElement.className = 'total-weight valid';
                submitBtn.disabled = false;
            } else {
                totalElement.className = 'total-weight invalid';
                submitBtn.disabled = true;
            }
        }

        function resetWeights() {
            document.getElementById('cf-weight').value = 0.4;
            document.getElementById('cb-weight').value = 0.3;
            document.getElementById('pb-weight').value = 0.2;
            document.getElementById('hb-weight').value = 0.1;
            
            updateWeight('cf');
            updateWeight('cb');
            updateWeight('pb');
            updateWeight('hb');
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTotalWeight();
        });
    </script>
</body>
</html>
